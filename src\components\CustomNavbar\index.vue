<script setup lang="ts">
// 获取屏幕边界到安全区域距离
const self = reactive({
  scrollTop: 0,
  statusBarHeight: 0,
  navBarHeight: 0,
})

const info = uni.getSystemInfoSync()
self.statusBarHeight = info.statusBarHeight
const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
self.navBarHeight =
  menuButtonInfo.bottom - info.statusBarHeight + (menuButtonInfo.top - info.statusBarHeight)

const props = defineProps<{
  title: string
}>()

onMounted(() => {
  uni.$on('onPageScroll', (data) => {
    if (data > 98) {
      self.scrollTop = data
    } else {
      self.scrollTop = 0
    }
  })
})
</script>

<template>
  <view>
    <view :style="{ height: self.statusBarHeight + self.navBarHeight + 'px' }"></view>
    <view class="navbar" :style="{ background: self.scrollTop > 98 ? '#F6F5FE' : '' }">
      <view :style="{ height: self.statusBarHeight + 'px' }"></view>
      <view class="navbar-content" :style="{ height: self.navBarHeight + 'px' }">
        <view class="logo">
          <image src="/static/images/logo.png" mode="scaleToFill" class="logo" />
        </view>
        <!-- <text class="title">{{ props.title }}</text> -->
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  transition: all 0.3s;
  .search {
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      font-size: 33rpx;
      font-weight: 400;
      color: #000000;
    }
  }
}
.navbar-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 30rpx;
  .logo {
    width: 380rpx;
    height: 44rpx;
  }
}
</style>
