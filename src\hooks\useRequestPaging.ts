import { ref, reactive, onMounted } from 'vue'

interface UseRequestPagingOptions {
  apiFunction: (params: any) => Promise<any>
  limit?: number
  initialParams?: any
  immediate?: boolean
}

export function useRequestPaging({
  apiFunction,
  limit = 10,
  initialParams = () => ({}),
  immediate = true,
}: UseRequestPagingOptions) {
  const data = ref([])
  const loading = ref(false)
  const loadEnd = ref(false)
  const pageDate = reactive({
    page: 1,
    limit,
    ...initialParams(),
  })

  const fetchData = async () => {
    loading.value = true
    try {
      const [response, err] = await apiFunction(pageDate)
      if (response) {
        if (response.data.list < limit) {
          loadEnd.value = true
        }
        data.value = [...data.value, ...response.data.list]
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      loading.value = false
    }
  }
  const resetParams = (newParams: any = {}) => {
    data.value = []
    loadEnd.value = false
    Object.assign(pageDate, { page: 1, limit, ...initialParams(), ...newParams })
    fetchData()
  }

  const loadMore = () => {
    if (!loadEnd.value) {
      pageDate.page += 1
      fetchData()
    }
  }

  onMounted(() => {
    immediate && fetchData()
  })

  return {
    data,
    loading,
    loadEnd,
    loadMore,
    pageDate,
    resetParams,
  }
}
