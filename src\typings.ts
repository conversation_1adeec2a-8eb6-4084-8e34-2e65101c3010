// 全局要用的类型放到这里

type IResData<T> = {
  code: number
  msg: string
  data: T
}

// uni.uploadFile文件上传参数
type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}

type IUserInfo = {
  nickname?: string
  avatar?: string
  /** 微信的 openid，非微信没有这个字段 */
  openid?: string
  token?: string
  birthday?: number
  wechat_code?: string
  phone?: string
  spread_code?: string | number
  spread_spid?: string | number
  life_img?: string
  sex?: '男' | '女' | '保密'
  age?: string
  home?: string
  school?: string
  annual_income?: string
  hobby?: string
  educational?: string
  province_id?: string | number
  city_id?: string | number
  district_id?: string | number
  home_province_id?: string | number
  home_city_id?: string | number
  home_district_id?: string | number
}
type ILogin = {
  bindName: boolean
  expires_time: number
  token: string
}
type IShareInfo = {
  spreadCode: string | number
  spreadSpid: string | number
}

enum TestEnum {
  A = 'a',
  B = 'b',
}
