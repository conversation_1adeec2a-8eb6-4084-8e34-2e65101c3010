<template>
  <wd-popup
    v-model="show"
    custom-style="border-radius:32rpx 32rpx 0 0;"
    position="bottom"
    :close-on-click-modal="false"
  >
    <view class="privacy-popup-mask">
      <view class="privacy-popup-container">
        <view class="privacy-popup-title">用户隐私保护提示</view>
        <view class="privacy-popup-content">
          感谢您使用本小程序，您使用本程序前应当阅读并同意
          <text class="privacy-popup-link" @click="openPrivacyGuide">《用户隐私保护指引》</text>
          <view class="privacy-popup-desc">
            当您点击同意并开始时用户产品服务时，即表示你已理解并同意该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用并退出小程序。
          </view>
        </view>
        <view class="privacy-popup-actions">
          <button class="agree-btn" @click="onAgree">同意授权</button>
          <button class="reject-btn" @click="onReject">拒绝授权</button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
//
const show = ref(false)
const userStore = useUserStore()
const { isAgreePrivacy } = storeToRefs(userStore)

if (!isAgreePrivacy.value) {
  show.value = true
}
function onAgree() {
  //
  show.value = false
  userStore.isAgreePrivacy = true
}
function onReject() {
  // 这里可以调用 Taro/uniapp 的退出小程序API，或由父组件处理
  uni.exitMiniProgram()
}
function openPrivacyGuide() {
  // 跳转到隐私指引页面或打开链接
  // 这里可 emit 或直接跳转
  uni.navigateTo({ url: '/pages/users/agreement?title=隐私协议&id=3' })
}
</script>

<style lang="scss" scoped>
.privacy-popup-container {
  padding: 28rpx;
}
.privacy-popup-title {
  margin-bottom: 32rpx;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}
.privacy-popup-content {
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #333;
}
.privacy-popup-link {
  margin: 0 4rpx;
  color: #4a90e2;
  text-decoration: underline;
}
.privacy-popup-desc {
  margin-top: 16rpx;
  font-size: 26rpx;
  color: #666;
}
.privacy-popup-actions {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 24rpx;
}
.agree-btn {
  height: 80rpx;
  font-size: 32rpx;
  line-height: 80rpx;
  color: #fff;
  background: #21c06e;
  border: none;
  border-radius: 8rpx;
}
.reject-btn {
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #ff3b30;
  background: transparent;
  border: none;
}
</style>
