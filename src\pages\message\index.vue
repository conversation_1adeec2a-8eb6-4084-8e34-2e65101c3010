<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="wrapper">
    <!-- <CustomNavbar title="消 息" /> -->
    <KeFu />
    <view class="px-5 relative z-10 pt-5">
      <!-- <view class="tab">
        <view class="tab__item">
          <button type="button" hover-class="button-hover" class="btn active">系统消息</button>
        </view>
        <view class="tab__item">
          <button type="button" hover-class="button-hover" class="btn" @click="handlePrivateChat">
            我的私信
          </button>
        </view>
      </view> -->
      <view class="body">
        <view class="search-box">
          <view class="input-box">
            <image
              src="/static/images/icon-search.png"
              mode="widthFix"
              class="w-[25rpx] h-[25rpx]"
            />
            <input
              class="input"
              v-model="keyWord"
              placeholder="消息标题"
              placeholder-class="input-placeholder"
            />
            <view class="search-text" @click="onSearch">搜索</view>
          </view>
          <view class="btns">
            <view>
              <button type="button" hover-class="button-hover" class="btn center">全部已读</button>
            </view>
          </view>
        </view>
        <view class="ul" v-for="item in data" :key="item.id">
          <view class="li">
            <view class="icon">
              <image src="/static/images/icon-message-2.png" class="image" mode="widthFix"></image>
            </view>
            <view class="info">
              <view class="title">系统消息-{{ item.title }}</view>
              <view class="desc">{{ item.content }}</view>
            </view>
            <view class="operation">
              <view class="timer">{{ item.add_time }}</view>
              <view class="status">
                <view class="badge"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavbar from '@/components/CustomNavbar/index.vue'
import { useRequestPaging } from '@/hooks/useRequestPaging'
import { getMessageList } from '@/service/public'
import KeFu from '@/components/KeFu/index.vue'

//
const keyWord = ref('')

const { data, loading, loadMore, resetParams } = useRequestPaging({
  apiFunction: getMessageList,
  immediate: false,
})

onShow(() => {
  resetParams()
})

onReachBottom(() => {
  loadMore()
})
//
const onSearch = () => {
  // 搜索
  resetParams({ title: keyWord.value })
}
const handlePrivateChat = () => {}
</script>

<style lang="scss" scoped>
//
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-bottom: 40rpx;
  background-color: #f3f2fd;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 442rpx;
    content: '';
    // 渐变从左上角到右下角
    background: url('https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/attach/2025/08/d9ce220250804112831395.png')
      no-repeat;
    background-size: 100% 100%;
  }
}
.tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  &__item {
    flex: 1;
    &:last-child {
      margin-left: 62rpx;
    }
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 71rpx;
      font-size: 28rpx;
      font-weight: normal;
      color: #f9225e;
      border: 1rpx solid #fb225e;
      border-radius: 36rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
    .active {
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border: none;
    }
  }
}
.body {
  .ul {
    padding: 30rpx 0;
    margin-top: 40rpx;
    background: #ffffff;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

    .li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      //   height: 168rpx;
      padding: 20rpx 40rpx;

      &:not(:first-child) {
        border-top: 1rpx solid #f5f5f5;
      }

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 111rpx;
        height: 111rpx;
        margin-right: 26rpx;
        background: linear-gradient(-31deg, #ff94b2, #f9225e);
        border-radius: 50%;
        box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

        .image {
          width: 54rpx;
          height: 62rpx;
        }
      }

      .info {
        flex: 1;

        .title {
          font-size: 26rpx;
          color: #303030;
        }

        .desc {
          margin-top: 10rpx;
          font-size: 22rpx;
          color: #7f7f7f;
        }
      }

      .operation {
        margin-left: 28rpx;

        .timer {
          font-size: 20rpx;
          color: #7f7f7f;
        }

        .status {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-top: 38rpx;

          .badge {
            width: 20rpx;
            height: 20rpx;
            background: linear-gradient(-31deg, #ff94b2, #f9225e);
            border-radius: 50%;
            box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
          }
        }
      }
    }
  }
}

.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .input-box {
    display: flex;
    flex: 1;
    align-items: center;
    height: 64rpx;
    padding: 18rpx 24rpx;
    margin-right: 20rpx;
    background: #ffffff;
    border-radius: 32rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.13);

    .input {
      margin-left: 10rpx;
    }

    .search-text {
      position: relative;
      padding-left: 10rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #ff225e;

      &::after {
        position: absolute;
        top: 50%;
        left: 0;
        display: block;
        width: 3rpx;
        height: 80%;
        content: '';
        background: linear-gradient(-31deg, #ff94b2, #f9225e);
        transform: translateY(-50%);
      }
    }
  }

  .btns {
    margin-right: 18rpx;

    .btn {
      width: 143rpx;
      height: 64rpx;
      font-size: 20rpx;
      font-weight: normal;
      color: #ffffff !important;
      background: linear-gradient(-31deg, #ff94b2, #f9225e);
      border-radius: 32rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }
}

:deep(.input-placeholder) {
  font-size: 25rpx;
  font-weight: 400;
  color: #aaaaaa;
}
</style>
