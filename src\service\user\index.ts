import { http } from '@/utils/http'
import { pretty } from '@/utils/index'

enum Api {
  personalDataSet = '/api/xq/personalDataSet',
  activityList = '/api/xq/activityList',
  activityDetail = '/api/xq/activityDetail',
  joinActivity = '/api/xq/joinActivity',
  pay = '/api/xq/pay',
  myActivityList = '/api/xq/myActivityList',
  myDelJoin = '/api/xq/myDelJoin',
  myCancelJoin = '/api/xq/myCancelJoin',
  personalDataEdit = '/api/xq/personalDataEdit',
  indexUser = '/api/xq/indexUser',
  userDetail = '/api/xq/userDetail',
  userCardList = '/api/xq/myCardList',
  commentListArticle = '/api/xq/commentListArticle',
  sendCommentArticle = '/api/xq/sendCommentArticle',
  attentionFiled = '/api/xq/attentionFiled',
  attentionSub = '/api/xq/attentionSub',
  userAttentionList = '/api/xq/userAttentionList',
}

/**
 * 设置个人资料
 */
export const personalDataSet = (params) => {
  return pretty(http.post(Api.personalDataSet, params))
}

type IActive = {
  type: 0 | 1 | 2
  page: number
  limit: number
  [key: string]: any
}

/**
 * 评论列表
 */
export const commentListArticle = (nid, params) => {
  return pretty(http.get(`${Api.commentListArticle}/${nid}`, params))
}

/**
 * 提交评论
 */
export const sendCommentArticle = (nid, params) => {
  return pretty(http.post(`${Api.sendCommentArticle}/${nid}`, params))
}

/**
 * 活动列表
 */
export const activityList = (params: IActive) => {
  return pretty(http.get(Api.activityList, params))
}

/**
 * 活动详情
 */
export const activityDetail = (id) => {
  return pretty(http.get(`${Api.activityDetail}/${id}`))
}

/**
 * 报名活动
 */
export const joinActivity = (nid, params) => {
  return pretty(http.post(`${Api.joinActivity}/${nid}`, params))
}
/**
 *报名支付 - 报名成功后调用支付
 */
export const pay = (id) => {
  return pretty(http.post(`${Api.pay}/${id}`))
}
/**
 *我的报名 - 列表
 */
export const myActivityList = (params) => {
  return pretty(http.get(`${Api.myActivityList}`, params))
}

/**
 *我的报名 - 删除
 */
export const myDelJoin = (id) => {
  return pretty(http.post(`${Api.myDelJoin}/${id}`))
}

/**
 *我的报名 - 取消
 */
export const myCancelJoin = (id) => {
  return pretty(http.post(`${Api.myCancelJoin}/${id}`))
}
/**
 *编辑个人资料
 */
export const personalDataEdit = (params) => {
  return pretty(http.post(Api.personalDataEdit, params))
}

/**
 *首页推荐用户
 */
export const indexUser = (params) => {
  return pretty(http.get(Api.indexUser, params))
}
/**
 *查看用户详情
 */
export const userDetail = (uid) => {
  return pretty(http.get(`${Api.userDetail}/${uid}`))
}
/**
 * 用户会员卡列表
 */
export const userCardList = (params) => {
  return pretty(http.get(Api.userCardList, params))
}
/**
 * 社群 - 兴趣爱好 - 选项表单
 */
export const attentionFiled = (shequnTypeField) => {
  return pretty(http.get(`${Api.attentionFiled}/${shequnTypeField}`))
}
/**
 * 社群 - 兴趣爱好 - 提交表单
 */
export const attentionSub = (shequnTypeField, params) => {
  return pretty(http.post(`${Api.attentionSub}/${shequnTypeField}`, params))
}

export const userAttentionList = (params) => {
  return pretty(http.get(Api.userAttentionList, params))
}
