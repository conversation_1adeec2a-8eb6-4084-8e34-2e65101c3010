<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '个人详情',
  },
}
</route>

<template>
  <view class="f-wrap" v-if="indexUserInfo.uid">
    <KeFu />
    <wd-swiper
      :list="indexUserInfo.life_img"
      autoplay
      :height="375"
      v-model:current="current"
      @click="handleClick"
      @change="onChange"
    >
      <template #indicator="{ current, total }">
        <view class="custom-indicator">{{ current + 1 }}/{{ total }}</view>
        <!-- <view class="upload">
          <button type="button" hover-class="button-hover" class="btn-upload">
            <text class="iconfont icon-xiangji"></text>
            上传生活照
          </button>
        </view> -->
      </template>
    </wd-swiper>
    <view class="user-info">
      <view class="head">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-user-info.png"
          mode="widthFix"
        ></image>
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">基本资料</text>
      </view>
      <view class="flex justify-between items-center mt-[38rpx]">
        <view class="label">性别</view>
        <view class="desc flex items-center">
          <view class="info mr-[12rpx]">
            <!-- <input type="text" class="input" value="女" /> -->
            <text v-if="indexUserInfo.sex === 0">保密</text>
            <text v-else-if="indexUserInfo.sex === 1">男</text>
            <text v-else>女</text>
          </view>
        </view>
      </view>
      <view class="flex justify-between items-center mt-[60rpx]">
        <view class="label">年龄</view>
        <view class="desc flex items-center">
          <view class="info mr-[12rpx]">{{ indexUserInfo.birthday_txt }}</view>
        </view>
      </view>
      <view class="flex justify-between items-center mt-[60rpx]">
        <view class="label">院校</view>
        <view class="desc flex items-center">
          <view class="info mr-[12rpx]">{{ indexUserInfo.educational }}</view>
        </view>
      </view>
      <view class="flex justify-between items-center mt-[60rpx]">
        <view class="label">所在地</view>
        <view class="desc flex items-center">
          <view class="info mr-[12rpx]">{{ indexUserInfo.city_txt }}</view>
        </view>
      </view>
      <view class="flex justify-between items-center mt-[60rpx]">
        <view class="label">家乡</view>
        <view class="desc flex items-center">
          <view class="info mr-[12rpx]">{{ indexUserInfo.home_city_txt }}</view>
        </view>
      </view>
      <view class="flex justify-between items-center mt-[60rpx]">
        <view class="label">年收入</view>
        <view class="desc flex items-center">
          <view class="info mr-[12rpx]">{{ getAnnualIncome }}</view>
        </view>
      </view>
    </view>
    <view class="hobby">
      <view class="head">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-hobby.png"
          mode="widthFix"
        ></image>
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">爱好</text>
      </view>
      <view class="mt-2">
        <view class="hobby-content">
          {{ indexUserInfo.hobby }}
        </view>
      </view>
    </view>
    <!-- <view class="wrap">
      <view class="head">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-renzehng.png"
          mode="widthFix"
        ></image>
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">认证</text>
      </view>
      <view class="mt-2">
        <view class="ul">
          <view class="li flex items-center justify-between">
            <view class="flex items-center">
              <image
                class="w-[45rpx] h-[39rpx]"
                src="/static/images/link-realname.png"
                mode="widthFix"
              ></image>
              <text class="label">实名认证</text>
            </view>
            <view class="flex items-center">
              <image
                class="w-[44rpx] h-[38rpx] ml-[20rpx]"
                src="/static/images/link-renzehng.png"
                mode="widthFix"
              ></image>
            </view>
          </view>
          <view class="li flex items-center justify-between mt-[56rpx]">
            <view class="flex items-center">
              <image
                class="w-[45rpx] h-[39rpx]"
                src="/static/images/link-education.png"
                mode="widthFix"
              ></image>
              <text class="label">学历认证</text>
            </view>
            <view class="flex items-center">
              <text class="place">大学本科</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
    <!-- 联系我板块 -->
    <view class="send-message-container">
      <button hover-class="button-hover" class="send-message" @click="handlePrivateChat">
        私聊我
        <wd-icon name="dong" color="#fff"></wd-icon>
      </button>
      <wd-gap safe-area-bottom height="0"></wd-gap>
    </view>

    <!-- 验证用户是否完成这个分类的认证，未认证就认证，认证了就显示群二维码 -->
    <wd-popup
      v-model="showInviterPopup"
      custom-style="border-radius: 30rpx;"
      :close-on-click-modal="false"
    >
      <view class="inviter-popup">
        <view class="close-icon" @click="showInviterPopup = false">
          <wd-icon name="close" size="20px" color="#999"></wd-icon>
        </view>
        <view class="popup-title">该用户已加入社群：</view>
        <view class="popup-content">
          <text>{{ indexUserInfo.shequn_name }}</text>
        </view>
        <view class="popup-footer">
          <button class="submit-btn" hover-class="button-hover" @click="handleInviterSubmit">
            我也去加入
          </button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { useToast, useMessage } from 'wot-design-uni'
import { userDetail } from '@/service/user/index'
import { INCOME_LIST } from '@/constants/index'
import KeFu from '@/components/KeFu/index.vue'
//
const message = useMessage()
const toast = useToast()
const current = ref<number>(0)
const { onShareAppMessage, onShareTimeline } = useShare()

const indexUserInfo = ref<any>({})

const showInviterPopup = ref(false)
const handleInviterSubmit = async () => {
  // 关闭弹窗
  showInviterPopup.value = false
  // 跳转到社群页面
  uni.navigateTo({
    url: '/pages/shequn/detail?id=' + indexUserInfo.value.shequn_id,
  })
}

const getAnnualIncome = computed(() => {
  const findItem = INCOME_LIST.find((item) => item.id === indexUserInfo.value.annual_income)
  if (!findItem) return ''
  return findItem.label
})
function handleClick(e) {
  console.log(e)
}
function onChange(e) {
  console.log(e)
}
const loadUserDetail = async (uid) => {
  const [res, err] = await userDetail(uid)
  if (res) {
    console.log(res)
    res.data.life_img = res.data.life_img.split(',')
    indexUserInfo.value = res.data
    console.log('🚀 ~ loadUserDetail ~   indexUserInfo.value:', indexUserInfo.value)
  }
}
const handlePrivateChat = () => {
  if (!indexUserInfo.value.shequn_name) {
    showInviterPopup.value = false
    toast.warning('该用户暂未加入任何社群')
  } else {
    showInviterPopup.value = true
  }
}

onLoad(({ uid }) => {
  console.log('🚀 ~ onLoad ~ uid:', uid)
  loadUserDetail(uid)
})
</script>

<style>
page {
  background-color: #f2f2f2;
}
.wd-textarea {
  --wot-textarea-bg: #f5f5f5;
  border-radius: 50rpx;
  --wot-textarea-cell-padding: 38rpx;
  --wot-textarea-padding: 40rpx;
}
</style>
<style lang="scss" scoped>
//
.custom-indicator {
  position: absolute;
  right: 333rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 49rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
}
.btn-upload {
  position: absolute;
  right: 30rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 218rpx;
  height: 49rpx;
  padding: 0;
  margin: 0;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
  .iconfont {
    margin-right: 10rpx;
  }
}
.user-info {
  padding: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;
  transform: translateY(-120rpx);
}
.input {
  text-align: right;
}
.hobby {
  padding: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;
  transform: translateY(-100rpx);
}
.hobby-content {
  font-size: 30rpx;
  font-weight: 400;
  color: #333333;
}
.wrap {
  padding: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;
  transform: translateY(-80rpx);
  .ul {
    padding: 53rpx 24rpx;
    background: #ffffff;
    border-radius: 11rpx;
    .label {
      margin-left: 20rpx;
      font-size: 30rpx;
      font-weight: 400;
      color: #312f35;
    }
    .place {
      font-size: 30rpx;
      font-weight: 400;
      color: #cccccc;
    }
  }
}
.send-message-container {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 28rpx 28rpx 10rpx;
  background: #fff;
}
.send-message {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 98rpx;
  color: white;
  background: linear-gradient(-31deg, #ff94b2, #f9225e);
  border-radius: 16rpx;
}
.f-wrap {
  padding-bottom: 120rpx;
}
.inviter-popup {
  position: relative;
  width: 600rpx;
  padding: 60rpx 40rpx 40rpx;
  background: #fff;
  border-radius: 30rpx;

  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    padding: 10rpx;
    cursor: pointer;
  }

  .popup-title {
    margin-bottom: 50rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  .popup-content {
    width: 520rpx;
    height: 100rpx;
    text-align: center;
    // margin-bottom: 50rpx;

    .input-item {
      margin-bottom: 30rpx;

      :deep(.wd-input) {
        padding: 16rpx 24rpx;
        background: #f8f8f8;
        border-radius: 16rpx;
        transition: all 0.3s ease;
      }

      :deep(.wd-input__label) {
        font-weight: 500;
        color: #333;
      }

      :deep(.wd-input__inner) {
        height: 88rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        background: transparent;
        border: none;
        border-radius: 8rpx;
        transition: all 0.3s ease;
      }
    }
  }

  .popup-footer {
    margin-top: 50rpx;

    .submit-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      font-size: 32rpx;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border: none;
      border-radius: 44rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }
}
</style>
