<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '个人中心',
  },
}
</route>

<template>
  <view class="container">
    <KeFu />

    <wd-swiper
      :list="swiperList"
      autoplay
      :height="375"
      v-model:current="current"
      @click="handleClick"
      @change="onChange"
    >
      <template #indicator="{ current, total }">
        <view class="custom-indicator">{{ current + 1 }}/{{ total }}</view>
        <!-- <view class="upload">
          <button type="button" hover-class="button-hover" class="btn-upload">
            <text class="iconfont icon-xiangji"></text>
            上传生活照
          </button>
        </view> -->
      </template>
    </wd-swiper>
    <view class="user-info">
      <view class="head mb-2">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-user-info.png"
          mode="widthFix"
        ></image>
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">基本资料</text>
      </view>

      <wd-form ref="formDom" :model="form">
        <wd-cell-group custom-class="group" border>
          <wd-picker
            label="性别"
            placeholder="请选择"
            label-width="100px"
            prop="promotion"
            v-model="form.sex"
            :columns="sexList"
          />
          <wd-cell prop="age" title="年龄" title-width="100px" custom-value-class="cell-left">
            <wd-input no-border placeholder="请输入" disabled v-model="form.age" />
          </wd-cell>
          <wd-datetime-picker
            label="生日"
            label-width="100px"
            placeholder="请选择"
            prop="birthday"
            v-model="form.birthday"
            :minDate="minDate"
            @confirm="handleDateConfirm"
            type="date"
          />
          <wd-select-picker
            label="所在社群"
            placeholder="请选择"
            v-model="form.shequn_id"
            label-width="100px"
            prop="annual_income"
            :columns="shequnList"
            type="radio"
            :show-confirm="false"
            clearable
            filterable
          ></wd-select-picker>
          <wd-cell prop="age" title="院校" title-width="100px" custom-value-class="cell-left">
            <wd-input no-border placeholder="请输入" v-model="form.educational" />
          </wd-cell>
          <wd-col-picker
            label="所在地"
            placeholder="请选择"
            label-width="100px"
            prop="address"
            v-model="form.address"
            :columns="areaAddress"
            :column-change="columnChange"
            :display-format="displayFormat"
            @confirm="handleAddressConfirm"
          ></wd-col-picker>
          <wd-col-picker
            label="家乡"
            placeholder="请选择"
            label-width="100px"
            prop="home"
            v-model="form.home"
            :columns="areaHome"
            :column-change="columnChange"
            :display-format="displayFormat"
            @confirm="handleHomeConfirm"
          ></wd-col-picker>
          <wd-picker
            label="年收入"
            placeholder="请选择"
            label-width="100px"
            prop="annual_income"
            v-model="form.annual_income"
            :columns="incomeList"
          />
        </wd-cell-group>
      </wd-form>
    </view>
    <view class="hobby">
      <view class="head">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-hobby.png"
          mode="widthFix"
        ></image>
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">生活照</text>
      </view>
      <view class="mt-4 album">
        <block v-for="(src, index) in form.life_img" :key="index">
          <view class="album__item">
            <view class="album__item__img relative" v-if="src">
              <wd-img :width="107" :height="132" :src="src" :radius="6" enable-preview />
              <view class="absolute top-0 right-0" @click="delImg(index)">
                <wd-icon name="close-circle-filled" size="22px"></wd-icon>
              </view>
            </view>
          </view>
        </block>
        <view class="album__item">
          <view class="album__item__inner" @click="addPic">
            <view class="add">
              <wd-icon name="add" size="18px" color="#312F35"></wd-icon>
            </view>
            <view class="label">生活照</view>
          </view>
        </view>
      </view>
      <view class="head mt-4">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-hobby.png"
          mode="widthFix"
        ></image>
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">爱好</text>
      </view>
      <view class="mt-2">
        <view class="bg-[#f5f5f5] border-rd-[50rpx]">
          <wd-textarea placeholder="请输入" v-model="form.hobby" :maxlength="100" show-word-limit />
        </view>
        <view class="submit">
          <view class="mt-[30rpx]">
            <button
              type="button"
              hover-class="button-hover"
              @click="handleEdit"
              class="w-[480rpx] h-[99rpx] border-rd-[50rpx] flex items-center justify-center submit-btn"
            >
              修改资料
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/user'
import { useColPickerData } from '@/hooks/useColPickerData'
import { INCOME_LIST, SEX_LIST } from '@/constants/index'
import { useToast } from 'wot-design-uni'
import { personalDataSet, activityList } from '@/service/user/index'
import KeFu from '@/components/KeFu/index.vue'

//
const userStore = useUserStore()
const { colPickerData, findChildrenByCode } = useColPickerData()
const { onShareAppMessage, onShareTimeline } = useShare()
const { userInfo } = storeToRefs(useUserStore())
const toast = useToast()

// 上传hooks回调
const uploadCallback = (res) => {
  res = JSON.parse(res)
  form.life_img.push(res.data.url)
}

const { runUploadFile } = useUpload({}, uploadCallback)

const current = ref<number>(0)

const swiperList = ref([])

const form = reactive<{
  life_img: string[]
  sex: string
  age: number
  birthday: number
  educational: string
  address: any[]
  province_id: string | number
  city_id: string | number
  district_id: string | number
  home: any[]
  home_province_id: string | number
  home_city_id: string | number
  home_district_id: string | number
  annual_income: string
  hobby: string
  shequn_id: string | number
}>({
  life_img: [],
  sex: '',
  age: 0,
  birthday: 0,
  educational: '',
  address: [],
  province_id: '',
  city_id: '',
  district_id: '',
  home: [],
  home_province_id: '',
  home_city_id: '',
  home_district_id: '',
  annual_income: '',
  hobby: '',
  shequn_id: '',
})

const areaHome = ref<any[]>([])
const areaAddress = ref<any[]>([])
const sexList = ref<any[]>([...SEX_LIST])
const incomeList = ref<any[]>([...INCOME_LIST])
const shequnList = ref<any[]>([])

const loadShequnList = async () => {
  const [res, err] = await activityList({
    classify: 3,
    type: 0,
    page: 1,
    limit: 100,
    title: '',
    city_id: 0,
  })

  if (res) {
    console.log('res :>> ', res)
    shequnList.value = res.data.list.map((item: any) => {
      return {
        label: item.title,
        value: item.id,
      }
    })
  }
}
loadShequnList()

const minDate = new Date('1924/01/01').getTime()

/** ***********  ✨ Codeium Command ⭐  *************/
/**
 * columnChange回调函数
 * @param {{selectedItem: any, resolve: function, finish: function}} param
 * @returns {void}
 */

/** ****  5c5b5ee0-9c28-4a94-a32d-bad6e9cbced4  *******/
const columnChange = ({ selectedItem, resolve, finish }) => {
  const areaData = findChildrenByCode(colPickerData, selectedItem.value)
  if (areaData && areaData.length) {
    resolve(
      areaData.map((item) => {
        return {
          value: item.value,
          label: item.text,
        }
      }),
    )
  } else {
    finish()
  }
}

// 格式化方法
const displayFormat = (selectedItems: Record<string, any>[]) => {
  console.log('🚀 ~ displayFormat ~ selectedItems:', selectedItems)
  if (selectedItems.length === 0) return ''
  return selectedItems.map((item) => item.label).join('-')
}
function handleAddressConfirm({ value, selectedItems }) {
  form.province_id = value[0]
  form.city_id = value[1]
  form.district_id = value[2]
}
function handleHomeConfirm({ value, selectedItems }) {
  form.home_province_id = value[0]
  form.home_city_id = value[1]
  form.home_district_id = value[2]
}

function handleClick(e) {
  console.log(e)
}
function onChange(e) {
  console.log(e)
}

// 根据 birthday获取年龄
const getAge = (birthday: number) => {
  const now = new Date().getTime()
  const age = Math.floor((now - birthday) / 1000 / 60 / 60 / 24 / 365)
  return age
}
const getAnnualIncomeText = (id: string) => {
  const result = incomeList.value.find((item) => {
    return item.id === Number(id)
  })
  return result ? result.label : ''
}

const getDefaultData = () => {
  swiperList.value = userInfo.value.life_img.split(',')

  form.life_img = userInfo.value.life_img.split(',')
  // ---------
  form.province_id = userInfo.value.province_id
  form.city_id = userInfo.value.city_id
  form.district_id = userInfo.value.district_id

  // ---------------
  form.home_province_id = userInfo.value.home_province_id
  form.home_city_id = userInfo.value.home_city_id
  form.home_district_id = userInfo.value.home_district_id

  // ------------------
  form.sex = userInfo.value.sex
  form.birthday = userInfo.value.birthday * 1000
  form.age = getAge(form.birthday)
  form.educational = userInfo.value.educational
  form.annual_income = getAnnualIncomeText(userInfo.value.annual_income)
  form.hobby = userInfo.value.hobby
  form.shequn_id = userInfo.value.shequn_id
  form.address = [
    String(userInfo.value.province_id),
    String(userInfo.value.city_id),
    String(userInfo.value.district_id),
  ]
  form.home = [
    String(userInfo.value.home_province_id),
    String(userInfo.value.home_city_id),
    String(userInfo.value.home_district_id),
  ]
  areaAddress.value = [
    colPickerData.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, form.address[0])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, form.address[1])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
  ]
  areaHome.value = [
    colPickerData.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, form.home[0])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
    findChildrenByCode(colPickerData, form.home[1])!.map((item) => {
      return {
        value: item.value,
        label: item.text,
      }
    }),
  ]
}
onLoad(() => {
  getDefaultData()
})
const getAnnualIncomeId = (annualIncome) => {
  const result = incomeList.value.find((item) => item.value === annualIncome)
  return result ? result.id : 0
}

const handleEdit = async () => {
  toast.loading('请稍后...')

  const data = {
    nickname: userInfo.value.nickname,
    avatar: userInfo.value.avatar,
    sex: form.sex,
    birthday: form.birthday / 1000,
    educational: form.educational,
    province_id: form.province_id,
    city_id: form.city_id,
    district_id: form.district_id,
    home_province_id: form.home_province_id,
    home_city_id: form.home_city_id,
    home_district_id: form.home_district_id,
    hobby: form.hobby,
    life_img: form.life_img.join(','),
    annual_income: getAnnualIncomeId(form.annual_income),
    shequn_id: form.shequn_id,
  }
  console.log('🚀 ~ handleEdit ~ data:', data)

  const [res, err] = await personalDataSet(data)
  if (res) {
    toast.success('资料设置成功')
    userStore.getUserInfo()
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
  if (err) {
    toast.close()
  }
}
const delImg = (v) => {
  form.life_img.splice(v, 1)
}

//* 选择图片*//
const addPic = () => {
  uni.chooseImage({
    count: 9, // 最多可以选择的图片张数，默认9
    sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
    sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
    success: async (res) => {
      runUploadFile(res.tempFilePaths[0])
    },
  })
}
const handleDateConfirm = ({ value }) => {
  // 根据出生日期 算出年龄
  const birthDay = new Date(value)
  const today = new Date()

  const age = today.getFullYear() - birthDay.getFullYear()

  if (
    birthDay.getMonth() > today.getMonth() ||
    (birthDay.getMonth() === today.getMonth() && birthDay.getDate() > today.getDate())
  ) {
    return age - 1
  }
  form.age = age
}
</script>

<style>
page {
  background-color: #f2f2f2;
}
.wd-textarea {
  --wot-textarea-bg: #f5f5f5;
  border-radius: 50rpx;
  --wot-textarea-cell-padding: 38rpx;
  --wot-textarea-padding: 40rpx;
}
</style>
<style lang="scss" scoped>
//
.custom-indicator {
  position: absolute;
  right: 333rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 49rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
}
.btn-upload {
  position: absolute;
  right: 30rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 218rpx;
  height: 49rpx;
  padding: 0;
  margin: 0;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
  .iconfont {
    margin-right: 10rpx;
  }
}
.user-info {
  position: relative;
  z-index: 9;
  padding: 40rpx 30rpx;
  // transform: translateY(-120rpx);
  margin-top: -120rpx;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;
}
.input {
  text-align: right;
}
.hobby {
  padding: 40rpx 30rpx 8rpx;
  padding-bottom: 100rpx;
  // transform: translateY(-100rpx);
  margin-top: 20rpx;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;

  .submit-btn {
    font-size: 30rpx;
    font-weight: 400;
    color: #ffffff;
    background: linear-gradient(90deg, #ff94b2, #f9225e);
  }
}
.album {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  row-gap: 20rpx;
  column-gap: 20rpx;
  &__item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 215rpx;
    height: 265rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    &__inner {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      .add {
        display: flex;
        align-items: center;
        justify-content: center;

        width: 56rpx;
        height: 56rpx;
        background: #ffffff;
        border-radius: 50%;
      }
      .label {
        margin-top: 20rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #312f35;
      }
    }
  }
}
</style>
