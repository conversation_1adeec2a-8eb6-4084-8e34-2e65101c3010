@import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}
.pd-ios {
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom)) !important; ///兼容 IOS<11.2/
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom)) !important; ///兼容 IOS>11.2/
}

:root,
page {
  // 修改按主题色
  --wot-color-theme: #c5f355;
  background: #f5f5f5;
  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;
}
uni-page-body,
html,
body,
page {
  width: 100%;
  height: 100%;
}

.button-hover::after {
  background-color: rgba(0, 0, 0, 0.12) !important;
}

view,
text,
image {
  flex-shrink: 0;
}
view,
text {
  box-sizing: border-box;
}
