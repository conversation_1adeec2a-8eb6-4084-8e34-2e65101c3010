<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '个人中心',
    navigationStyle: 'custom',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <view class="wrapper">
    <CustomNavbar title="个人中心" />
    <KeFu />
    <view class="relative z-10">
      <view class="content">
        <view class="top">
          <view class="flex items-center justify-center flex-col">
            <view class="avatar__inner">
              <image class="w-[130rpx] h-[130rpx] border-rd-[50%]" :src="userInfo.avatar"></image>
              <!-- <view class="icon">
                <text class="iconfont icon-xiangji"></text>
              </view> -->
            </view>
            <view class="nickname">{{ userInfo.nickname }}</view>
            <!--进度条 -->
            <view class="progress">
              <view class="progress__text">资料完善度80%</view>
              <view class="progress__inner"></view>
            </view>
            <!-- 显示数据 -->
            <view class="info">
              <view class="info__item">
                <view class="label">我心动的</view>
                <view class="num">20</view>
              </view>
              <view class="info__item">
                <view class="label">心动我的</view>
                <view class="num">20</view>
              </view>
              <view class="info__item">
                <view class="label">我的活动</view>
                <view class="num">20</view>
              </view>
              <view class="info__item">
                <view class="label">最近来访</view>
                <view class="num">20</view>
              </view>
            </view>
          </view>
        </view>
        <view class="vip flex items-center px-_a_30rpx_a_ mt-_a_70rpx_a_">
          <image class="w-[30px] h-[30px]" src="/static/images/vip-top.png" mode="widthFix"></image>
          <view class="vip__info">
            <view class="vip__info__title">会员权益</view>
          </view>
          <view class="btns">
            <button type="button" hover-class="button-hover" class="btn" @click="handleBuyVip">
              点击查看
            </button>
          </view>
        </view>
        <view class="ul">
          <view class="li flex items-center justify-between" @click="toPage(1)">
            <view class="flex items-center">
              <image
                class="w-[35rpx] h-[26rpx]"
                src="/static/images/link-data.png"
                mode="widthFix"
              ></image>
              <text class="label">个人资料</text>
            </view>
            <view class="flex items-center">
              <text class="place">完善资料</text>
              <text class="iconfont icon-xiangyou arrow"></text>
            </view>
          </view>
          <view class="li flex items-center justify-between" @click="toPage(2)">
            <view class="flex items-center">
              <image
                class="w-[35rpx] h-[27rpx]"
                src="/static/images/link-realname.png"
                mode="widthFix"
              ></image>
              <text class="label">实名认证</text>
            </view>
            <view class="flex items-center">
              <text class="place">请认证</text>
              <text class="iconfont icon-xiangyou arrow"></text>
            </view>
          </view>
          <view class="li flex items-center justify-between" @click="toPage(3)">
            <view class="flex items-center">
              <image
                class="w-[36rpx] h-[25rpx]"
                src="/static/images/link-education.png"
                mode="widthFix"
              ></image>
              <text class="label">学历认证</text>
            </view>
            <view class="flex items-center">
              <text class="place">请认证</text>
              <!-- <text class="success-status">已认证</text> -->
              <text class="iconfont icon-xiangyou arrow"></text>
            </view>
          </view>
          <view class="li flex items-center justify-between" @click="toPage(7)">
            <view class="flex items-center">
              <image
                class="w-[36rpx] h-[25rpx]"
                src="/static/images/icon-message-3.png"
                mode="widthFix"
              ></image>
              <text class="label">我的消息</text>
            </view>
            <view class="flex items-center">
              <!-- <text class="place">请认证</text> -->
              <!-- <text class="success-status">已认证</text> -->
              <text class="iconfont icon-xiangyou arrow"></text>
            </view>
          </view>
          <view class="li flex items-center justify-between" @click="toPage(5)">
            <view class="flex items-center">
              <image
                class="w-[35rpx] h-[27rpx]"
                src="/static/images/link-xiyi-2.png"
                mode="widthFix"
              ></image>
              <text class="label">用户协议</text>
            </view>
            <view class="flex items-center">
              <text class="iconfont icon-xiangyou arrow"></text>
            </view>
          </view>
          <view class="li flex items-center justify-between" @click="toPage(4)">
            <view class="flex items-center">
              <image
                class="w-[35rpx] h-[28rpx]"
                src="/static/images/link-xiyi-1.png"
                mode="widthFix"
              ></image>
              <text class="label">隐私协议</text>
            </view>
            <view class="flex items-center">
              <text class="iconfont icon-xiangyou arrow"></text>
            </view>
          </view>
          <view class="li flex items-center justify-between" @click="onEdit">
            <view class="flex items-center">
              <image
                class="w-[45rpx] h-[39rpx]"
                src="/static/images/icon-seting.png"
                mode="widthFix"
              ></image>
              <text class="label">编辑资料</text>
            </view>
            <view class="flex items-center">
              <text class="iconfont icon-xiangyou"></text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import { useMessage } from 'wot-design-uni'
import CustomNavbar from '@/components/CustomNavbar/index.vue'
import KeFu from '@/components/KeFu/index.vue'
//
const { userInfo, isComplete } = storeToRefs(useUserStore())
const { onShareAppMessage, onShareTimeline } = useShare()
const message = useMessage()
const { safeAreaInsets } = uni.getSystemInfoSync()
const self = reactive({
  system: null,
  height: 0,
})

const system = ref({})
uni.getSystemInfo({
  success: (res) => {
    const menu = uni.getMenuButtonBoundingClientRect()
    self.system = res
    self.height = (menu.top - self.system.statusBarHeight) * 2 + menu.height
  },
})
const onEdit = () => {
  uni.navigateTo({ url: '/pages/users/edit-profile' })
}
const toPage = (status) => {
  switch (status) {
    case 1:
      uni.navigateTo({ url: '/pages/users/profile' })
      break
    case 2:
      uni.navigateTo({ url: '/pages/login/realname' })
      break
    case 3:
      uni.navigateTo({ url: '/pages/users/education' })
      break
    case 4:
      uni.navigateTo({ url: '/pages/users/agreement?title=隐私协议&id=3' })
      break
    case 5:
      uni.navigateTo({ url: '/pages/users/agreement?title=用户协议&id=4' })
      break
    case 7:
      uni.navigateTo({ url: '/pages/message/index' })
      break
  }
}
onLoad(() => {
  if (!isComplete.value) {
    message
      .confirm({
        msg: '您的资料不完整，暂时无法享受全部功能',
        title: '系统提醒',
        confirmButtonText: '立即完善',
        cancelButtonText: '稍后再说',
      })
      .then(() => {
        uni.navigateTo({ url: '/pages/login/information' })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
  }
})
const handleBuyVip = () => {
  uni.navigateTo({ url: '/pages/users/vip_card/index' })
}
const handleMyVip = () => {
  uni.navigateTo({ url: '/pages/users/vip_card/user_card' })
}
</script>

<style lang="scss">
//
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-bottom: 40rpx;
  background-color: #f3f2fd;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 442rpx;
    content: '';
    // 渐变从左上角到右下角
    background: linear-gradient(270deg, #f9225e, #ffb5ca);
    background-size: 100% 100%;
  }
}

.top {
  width: 100%;

  .avatar__inner {
    position: relative;
    border-radius: 50%;

    .icon {
      position: absolute;
      top: 92rpx;
      left: 91rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36rpx;
      height: 36rpx;
      background: #333333;
      border-radius: 50%;

      .iconfont {
        font-size: 22rpx;
        color: #fff;
      }
    }
  }

  .nickname {
    margin-top: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
  }

  .progress {
    position: relative;
    width: 100%;
    height: 10rpx;
    margin-top: 30rpx;
    background: #ffffff;
    border-radius: 5rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

    &__text {
      position: absolute;
      top: -30rpx;
      right: 0;
      font-size: 20rpx;
      font-weight: normal;
      color: #ffffff;
    }

    &__inner {
      width: 80%;
      height: 10rpx;
      background: linear-gradient(181deg, #ff94b2, #f9225e);
      border-radius: 5rpx;
    }
  }

  .info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 60rpx;
    margin-top: 50rpx;

    &__item {
      text-align: center;

      .label {
        font-size: 20rpx;
        color: #7f7f7f;
      }

      .num {
        margin-top: 24rpx;
        font-size: 32rpx;
        color: #303030;
      }
    }
  }
}

.content {
  padding: 40rpx 30rpx;

  .vip {
    width: 100%;
    height: 100rpx;
    padding: 30rpx 30rpx;
    background: url('/static/images/vip-bg.png') no-repeat;
    background-repeat: repeat-x;
    background-size: 100% 100%;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

    &__info {
      margin-left: 20rpx;
      .vip__info__title {
        font-size: 32rpx;
        font-weight: 500;
        color: #f0dcb9;
      }
      .vip__info__subtitle {
        font-size: 22rpx;
        color: #a9a9aa;
      }
    }
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 114rpx;
    height: 40rpx;
    padding: 0;
    margin: 0;
    margin-left: auto;
    font-size: 22rpx;
    color: #2c285c;
    background: white;
    border-radius: 14rpx;
  }

  .ul {
    padding: 40rpx 0;
    margin-top: 60rpx;
    background: #ffffff;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

    .arrow {
      margin-left: 20rpx;
      font-size: 26rpx;
      color: #7f7f7f;
    }

    .li {
      width: 100%;
      height: 90rpx;
      padding: 0 40rpx;

      &:not(:first-child) {
        border-top: 1rpx solid #f5f5f5;
      }
    }

    .label {
      margin-left: 20rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #312f35;
    }

    .place {
      font-size: 28rpx;
      color: #7f7f7f;
    }
  }
}

.success-status {
  font-size: 28rpx;
  color: #ff94b2;
}

.level-img {
  width: 140rpx;
  .image {
    width: 100%;
    height: 36rpx;
  }
}
.btns {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 166rpx;
    height: 50rpx;
    padding: 0;
    margin: 0 0 0 auto;
    margin-left: 20rpx;
    font-size: 32rpx;
    color: #3e414f;
    background-color: #f0dcb9;
    background-size: 100% 100%;
    border-radius: 25rpx;
  }
}
</style>
