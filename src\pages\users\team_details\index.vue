<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '跑团详情',
  },
}
</route>

<template>
  <view class="wrapper pb-[30rpx]">
    <!-- 顶部轮播图 -->
    <view class="card-swiper">
      <wd-swiper :list="teamInfo.images" autoplay :height="375" v-model:current="current">
        <template #indicator="{ current, total }">
          <view class="custom-indicator">{{ current + 1 }}/{{ total }}</view>
        </template>
      </wd-swiper>
    </view>
    <!-- 跑团资料 -->
    <view class="info">
      <view class="section-title px-4">
        <image
          class="w-[45rpx] h-[40rpx]"
          src="/static/images/icon-user-info.png"
          mode="widthFix"
        />
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">跑团资料</text>
      </view>
      <view class="px-4 mt-[30rpx]">
        <view class="flex items-center">
          <wd-img :src="teamInfo.logo" width="72" height="72" radius="8" mode="aspectFill" />
        </view>
        <view class="row mt-[40rpx]">
          <view class="label">团队名称</view>
          <view class="text">{{ teamInfo.name }}</view>
        </view>
        <view class="row mt-[40rpx]">
          <view class="label">所在城市</view>
          <view class="text">{{ teamInfo.city }}</view>
        </view>
        <view class="row mt-[40rpx]">
          <view class="label">当前人数</view>
          <view class="text">{{ teamInfo.people }} 人</view>
        </view>
        <view class="row-col mt-[40rpx]">
          <view class="label mb-[16rpx]">跑团介绍</view>
          <view class="desc">{{ teamInfo.desc }}</view>
        </view>
      </view>
    </view>
    <!-- 加入跑团：二维码 -->
    <view class="qrcode">
      <view class="section-title px-4">
        <image class="w-[45rpx] h-[40rpx]" src="/static/images/icon-aihao.png" mode="widthFix" />
        <text class="text-[#CCCCCC] text-[30rpx] font-400 ml-[20rpx]">加入跑团</text>
      </view>
      <view class="code-grid px-4 mt-[30rpx]">
        <view class="code-item">
          <view class="code-title">社群二维码</view>
          <view class="image-border">
            <image :src="teamInfo.group_qr" mode="scaleToFill" class="code-img" />
          </view>
        </view>
        <view class="code-item">
          <view class="code-title">联系人二维码</view>
          <view class="image-border">
            <image :src="teamInfo.contact_qr" mode="scaleToFill" class="code-img" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const current = ref<number>(0)

// 详情数据（等待后端接口对接时可通过路由参数带入或默认占位）
interface TeamInfo {
  images: any
  logo: string
  name: string
  city: string
  people: number | string
  desc: string
  group_qr: string
  contact_qr: string
}
const teamInfo = ref<TeamInfo>({
  images: ['https://dummyimage.com/750x750/3c9cff/fff'],
  logo: 'https://dummyimage.com/264x264/3c9cff/fff',
  name: '成都酷跑团',
  city: '成都',
  people: 340,
  desc: '成都酷跑团与On昂跑一起组织了城市夜跑。我们在成都太古里新开的On 昂跑旗舰店开启 CityRun，从繁华商圈跑到烟火街巷，直接被品牌细节和成都浪漫狠狠拿捏～',
  group_qr: 'https://dummyimage.com/264x264/3c9cff/fff',
  contact_qr: 'https://dummyimage.com/264x264/3c9cff/fff',
})

onLoad((options) => {
  // 可支持外部通过 data 传入 JSON 字符串填充页面
  if (options?.data) {
    try {
      const parsed = JSON.parse(decodeURIComponent(options.data))
      teamInfo.value = Object.assign(teamInfo.value, parsed)
      if (!Array.isArray(teamInfo.value.images) && typeof teamInfo.value.images === 'string') {
        teamInfo.value.images = teamInfo.value.images.split(',')
      }
    } catch (e) {
      // ignore
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-indicator {
  position: absolute;
  right: 333rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 49rpx;
  font-size: 26rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
}
.info {
  box-sizing: border-box;
  width: 100%;
  padding: 38rpx 0;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0 0;
  margin-top: -138rpx;
  position: relative;
  z-index: 9;
}
.section-title {
  display: flex;
  align-items: center;
}
.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.row .label {
  font-size: 30rpx;
  color: #312f35;
}
.row .text {
  font-size: 30rpx;
  color: #7a7a7a;
}
.row-col .label {
  font-size: 30rpx;
  color: 312f35;
}
.row-col .desc {
  font-size: 30rpx;
  color: #818181;
  line-height: 1.7;
}
.qrcode {
  padding: 38rpx 0;
  background: #ffffff;
  border-radius: 39rpx;
  margin-top: 20rpx;
}
.code-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.code-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.code-title {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #999;
}
.image-border {
  background: #c5f355;
  border-radius: 11rpx;
  border: 1px solid #ffffff;
  padding: 4rpx;
  .code-img {
    width: 264rpx;
    height: 264rpx;
    vertical-align: bottom;
  }
}
</style>
