<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '奔爱',
  },
}
</route>

<template>
  <view class="form-container">
    <!-- 提示信息 -->
    <view class="tip-text">*请完成兴趣认证后进群</view>
    <view class="page-container">
      <!-- 群名 -->
      <view class="group-name">
        <text class="label">群名：</text>
        <text class="name">{{ activityInfo.title }}</text>
      </view>
      <!-- 表单区域 -->
      <view class="form-section">
        <view v-for="item in formFields" :key="item.id" class="form-item">
          <!-- 选择框 -->
          <template v-if="item.type === 'select'">
            <wd-picker
              v-model="formData[item.id]"
              :columns="getSelectOptions(item.content)"
              :use-default-slot="true"
              @confirm="handleSelectConfirm(item.id, $event)"
            >
              <view class="form-field items-center">
                <view class="field-label-r">{{ item.name }}</view>
                <view class="field-content">
                  <text class="field-value">
                    {{ getSelectDisplayValue(item.id, item.content) || '请选择' }}
                  </text>
                  <wd-icon name="arrow-right" size="16px" color="#CCCCCC"></wd-icon>
                </view>
              </view>
            </wd-picker>
          </template>

          <!-- 输入框 -->
          <template v-if="item.type === 'input'">
            <view class="form-field items-center">
              <view class="field-label-r">{{ item.name }}</view>
              <view class="field-content">
                <input
                  type="text"
                  v-model="formData[item.id]"
                  :placeholder="item.content"
                  class="field-input"
                />
                <wd-icon name="arrow-right" size="16px" color="#CCCCCC"></wd-icon>
              </view>
            </view>
          </template>

          <!-- 上传组件 -->
          <template v-if="item.type === 'upload'">
            <view class="form-field upload-field">
              <view class="field-label">{{ item.name }}</view>
              <view class="upload-area">
                <!-- 循环显示3个位置 -->
                <view
                  v-for="index in 3"
                  :key="index"
                  class="upload-item"
                  :class="getUploadedImage(item.id, index - 1) ? 'uploaded' : 'add-btn'"
                  @click="
                    !getUploadedImage(item.id, index - 1) ? chooseImage(item.id, index - 1) : null
                  "
                >
                  <!-- 已上传的图片 -->
                  <template v-if="getUploadedImage(item.id, index - 1)">
                    <wd-img
                      :width="100"
                      :height="128"
                      :src="getUploadedImage(item.id, index - 1).url"
                      :radius="8"
                      enable-preview
                    />
                    <view class="delete-btn" @click.stop="deleteImage(item.id, index - 1)">
                      <wd-icon name="close-circle-filled" size="18px"></wd-icon>
                    </view>
                  </template>
                  <!-- 上传占位 -->
                  <template v-else>
                    <view class="upload-icon">
                      <wd-icon name="image" size="24px" color="#999"></wd-icon>
                    </view>
                    <view class="upload-text">{{ getUploadText(item.content, index - 1) }}</view>
                  </template>
                </view>
              </view>
            </view>
          </template>

          <!-- 备注信息 -->
          <template v-if="item.type === 'note'">
            <view class="form-field note-field">
              <view class="field-label-r" style="color: red">{{ item.name }}</view>
              <view class="note-content">
                <text
                  v-for="(line, lineIndex) in item.content.split('\n')"
                  :key="lineIndex"
                  class="note-line"
                >
                  {{ line }}
                </text>
              </view>
            </view>
          </template>
        </view>
      </view>
      <!-- 底部提示和提交按钮 -->
      <view class="bottom-section">
        <view class="submit-tip">*提交后不可更改</view>
        <button type="button" hover-class="button-hover" class="submit-btn" @click="handleSubmit">
          提交
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { activityDetail, attentionFiled, attentionSub } from '@/service/user/index'
import { useToast } from 'wot-design-uni'
import useUpload from '@/hooks/useUpload'

const toast = useToast()

// 表单字段配置
const formFields = ref<
  Array<{
    id: number
    type: string
    name: string
    content: string
  }>
>([])

const activityInfo = ref({
  id: '',
  image_input: [],
  title: '',
  content: '',
  start_time: '',
  address: '',
  price: 0,
  biaoqian: [],
  num: 0,
  add_time_date: '',
  visit: 0, // 浏览量
  checkHas: 'no',
  shequn_type_field: '',
})
const activityId = ref(0)

const loadDetail = async (id) => {
  const [res, err] = await activityDetail(id)
  if (res && res.data) {
    activityInfo.value = {
      id: res.data.id || '',
      image_input: res.data.image_input || [],
      title: res.data.title || '',
      content: res.data.content || '',
      start_time: res.data.start_time || '',
      address: res.data.address || '',
      price: res.data.price || 0,
      biaoqian: res.data.biaoqian || [],
      num: res.data.num || 0,
      add_time_date: res.data.add_time_date || '',
      visit: res.data.visit || 0,
      checkHas: res.data.checkHas || 'no',
      shequn_type_field: res.data.shequn_type_field || '',
    }
    loadAttentionFiled(activityInfo.value.shequn_type_field)
  }
}

const loadAttentionFiled = async (shequnTypeField) => {
  const [res, err] = await attentionFiled(shequnTypeField)
  if (res) {
    formFields.value = res.data
    // 初始化表单数据
    formFields.value.forEach((field) => {
      if (field.type === 'input') {
        // input类型使用content作为默认值
        formData[field.id] = ''
      } else if (field.type === 'upload') {
        // 初始化上传数据为3个位置
        uploadedImages[field.id] = [null, null, null]
      }
    })
  }
}

onLoad(({ id }) => {
  activityId.value = id
  loadDetail(id)
})

// 表单数据
const formData = reactive<Record<number, any>>({})

// 上传的图片数据
const uploadedImages = reactive<Record<number, Array<{ url: string } | null>>>({})

// 当前上传的字段ID和索引
const currentUploadFieldId = ref<number | null>(null)
const currentUploadIndex = ref<number | null>(null)

// 上传回调
const uploadCallback = (res: string) => {
  if (!currentUploadFieldId.value || currentUploadIndex.value === null) return

  try {
    if (typeof res !== 'string') {
      toast.error('上传失败：无效的响应')
      return
    }
    const response = JSON.parse(res)
    if (!response || !response.data || typeof response.data.url !== 'string') {
      toast.error('上传失败：无效的数据格式')
      return
    }
    if (!uploadedImages[currentUploadFieldId.value]) {
      uploadedImages[currentUploadFieldId.value] = []
    }
    uploadedImages[currentUploadFieldId.value][currentUploadIndex.value] = {
      url: response.data.url,
    }
    toast.success('上传成功')
  } catch (error) {
    console.error('解析上传结果失败:', error)
    toast.error('上传失败')
  } finally {
    currentUploadFieldId.value = null
    currentUploadIndex.value = null
  }
}

// 使用上传 hook
const { runUploadFile } = useUpload({}, uploadCallback)

// 获取选择框选项
const getSelectOptions = (content: string) => {
  return content.split(',').map((item) => ({
    value: item,
    label: item,
  }))
}

// 获取选择框显示值
const getSelectDisplayValue = (fieldId: number, content: string) => {
  const value = formData[fieldId]
  if (!value) return ''
  return value
}

// 处理选择框确认
const handleSelectConfirm = (fieldId: number, { value }) => {
  formData[fieldId] = value
}

// 获取上传按钮文本 - 从接口的content中获取
const getUploadText = (content: string, index: number) => {
  // 将content按逗号分割，获取对应索引的文本
  const textArray = content.split(',')
  return textArray[index] || `上传图片${index + 1}`
}

// 获取指定位置的已上传图片
const getUploadedImage = (fieldId: number, index: number) => {
  const images = uploadedImages[fieldId] || []
  return images[index] || null
}

// 选择图片
const chooseImage = (fieldId: number, index: number) => {
  currentUploadFieldId.value = fieldId
  currentUploadIndex.value = index
  uni.chooseImage({
    count: 1, // 每次只选择一张图片
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      runUploadFile(res.tempFilePaths[0])
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      toast.error('选择图片失败')
      currentUploadFieldId.value = null
      currentUploadIndex.value = null
    },
  })
}

// 删除图片
const deleteImage = (fieldId: number, index: number) => {
  if (uploadedImages[fieldId] && uploadedImages[fieldId][index]) {
    // 删除指定索引的图片，但保持数组结构
    uploadedImages[fieldId][index] = null
  }
}

// 表单验证
const isFormValid = computed(() => {
  // 检查必填字段
  for (const field of formFields.value) {
    if (field.type === 'select' || field.type === 'input') {
      if (formData[field.id] === '' || formData[field.id] === null) {
        return false
      }
    }
    if (field.type === 'upload') {
      const images = uploadedImages[field.id] || []
      // 检查是否至少有一张图片
      const hasValidImage = images.some((img) => img && img.url)
      if (!hasValidImage) {
        return false
      }
    }
  }
  return true
})

// 提交表单
const handleSubmit = async () => {
  if (!isFormValid.value) {
    toast.error('请完善所有必填信息')
    return
  }

  const submitData = ref<
    Array<{
      id: number
      type: string
      name: string
      content: string
    }>
  >([])
  for (const field of formFields.value) {
    const item = field
    if (field.type === 'select' || field.type === 'input') {
      item.content = formData[field.id]
    }
    if (field.type === 'upload') {
      const images = uploadedImages[field.id] || []
      // 检查是否至少有一张图片
      const hasValidImage = images.some((img) => img && img.url)
      item.content = hasValidImage ? images.map((img) => img && img.url).join(',') : ''
    }
    submitData.value.push(item)
  }

  // const submitData = {
  //   formData,
  //   uploadedImages,
  // }
  console.log('提交数据:', submitData.value)

  const content = { content: JSON.stringify(submitData.value) }

  const [res, err] = await attentionSub(activityInfo.value.shequn_type_field, content)
  if (res) {
    // 这里可以调用提交接口
    // 提交成功后可以跳转或返回
    toast.success('提交成功')
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}
</script>

<style lang="scss" scoped>
.tip-text {
  padding: 20rpx 030rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #f92560;
}

.page-container {
  padding: 40rpx 30rpx;
  background-color: white;
}
.group-name {
  margin-bottom: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #3a3e49;
}

.form-section {
  margin-bottom: 60rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-field {
  display: flex;
  padding: 40rpx 30rpx;
  background: #f5f5f5;
  border-radius: 16rpx;

  .field-label-r {
    margin-right: 28rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }
  .field-label {
    margin-bottom: 28rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }

  .field-content {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;

    .field-value {
      flex: 1;
      font-size: 28rpx;
      color: #666;
      text-align: right;
    }

    .field-input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      text-align: right;
    }
  }
}

.upload-field {
  flex-direction: column;

  .upload-area {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
  }

  .upload-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 200rpx;
    height: 250rpx;
    background: white;
    border-radius: 16rpx;

    &.add-btn {
      border: 2rpx dashed #ddd;

      .upload-icon {
        margin-bottom: 10rpx;
      }

      .upload-text {
        padding: 0 10rpx;
        font-size: 24rpx;
        line-height: 1.3;
        color: #999;
        text-align: center;
      }
    }

    &.uploaded {
      .delete-btn {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.note-field {
  .note-content {
    .note-line {
      display: block;
      margin-bottom: 10rpx;
      font-size: 28rpx;
      line-height: 1.6;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.bottom-section {
  padding: 30rpx;

  .submit-tip {
    margin-bottom: 20rpx;
    font-size: 26rpx;
    color: #999;
    text-align: center;
  }

  .submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    background: linear-gradient(90deg, #f9225e, #ff94b2);
    border: none;
    border-radius: 44rpx;

    &[disabled] {
      color: #fff;
      background: #f1d6d7;
    }

    &:not([disabled]):active {
      opacity: 0.8;
    }
  }
}

.button-hover {
  opacity: 0.8;
}
</style>
