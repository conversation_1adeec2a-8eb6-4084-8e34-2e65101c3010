<template>
  <!-- 可拖动 -->
  <movable-area class="kefu-area">
    <movable-view
      class="kefu-view"
      direction="all"
      :x="position.x"
      :y="position.y"
      damping="10"
      @change="handleMove"
      @touchend="handleMoveEnd"
    >
      <image src="/static/images/kefu.png" mode="widthFix" alt="客服图标" />
      <button class="kefu-button" open-type="contact">联系我们</button>
    </movable-view>
  </movable-area>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
//
const { isLogined, getCustomerInfo } = storeToRefs(useUserStore())
// 状态
const position = ref({
  x: 300,
  y: 400,
})
const windowSize = ref({
  width: 300,
  height: 400,
})

// 方法
const handleMove = (e) => {
  if (e.detail.source === 'touch') {
    position.value.x = e.detail.x
    position.value.y = e.detail.y
  }
}

const handleMoveEnd = () => {
  // setTimeout(() => {
  position.value.x = windowSize.value.width
  // }, 100)
}

const handleKefuClick = () => {
  if (!isLogined.value) {
    uni.navigateTo({ url: '/pages/users/login/index' })
  }
}

// 生命周期
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      const width = res.windowWidth - 50
      const height = res.windowHeight - 100
      windowSize.value = { width, height }
      position.value = { x: width, y: height }
    },
  })
})
</script>

<style lang="scss" scoped>
$theme-bg-color: #f9225e;

.kefu-area {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999999;
  display: flex;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.kefu-view {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 88rpx;
  height: 88rpx;
  pointer-events: auto;
  background-color: #fff;
  border-radius: 50%;

  image {
    width: 80%;
    height: 80%;
    border-radius: 50rpx;
  }
}

.kefu-badge {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30rpx;
  height: 30rpx;
  font-size: 24rpx;
  color: #fff;
  background: $theme-bg-color;
  border-radius: 50%;
}

.kefu-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
  overflow: hidden;
  border-radius: 100%;
  opacity: 0;
}
</style>
