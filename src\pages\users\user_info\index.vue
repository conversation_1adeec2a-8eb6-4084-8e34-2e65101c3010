<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="px-[30rpx] py-[20rpx]">
    <KeFu />

    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">性别:</view>
      <view class="flex items-center justify-between mt-[40rpx]">
        <view
          class="bg-[#F5F5F5] border-rd-[41rpx] w-[325rpx] flex flex-col items-center justify-center"
          @click="model.sex = '男'"
        >
          <view class="font-bold font-[40rpx] text-[#3A3E49] text-center py-[40rpx]">男</view>
          <image
            class="w-[262rpx] h-[299rpx]"
            :src="`/static/images/${model.sex === '男' ? 'login-male-select' : 'login-male-default'}.png`"
          ></image>
        </view>
        <view
          class="bg-[#F5F5F5] border-rd-[41rpx] w-[325rpx] flex flex-col items-center justify-center"
          @click="model.sex = '女'"
        >
          <view class="font-bold font-[40rpx] text-[#3A3E49] text-center py-[40rpx]">女</view>
          <image
            class="w-[262rpx] h-[299rpx]"
            :src="`/static/images/${model.sex === '女' ? 'login-female-select' : 'login-female-default'}.png`"
          ></image>
        </view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">出生日期</view>
      <view class="mt-[40rpx]">
        <wd-datetime-picker-view
          v-model="model.birthday"
          :formatter="formatter"
          label="日期选择"
          type="date"
          :minDate="minDate"
          @change="handleDateChange"
          :columns-height="238"
        />
      </view>
      <view class="text-[30rpx] text-[#312F35] font-bold pl-[50rpx] mt-[76rpx]">
        {{ getAge }}岁
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">请问您的学校是？</view>
      <view class="mt-[50rpx]">
        <view class="h-[99rpx] bg-[#F5F5F5] border-rd-[50rpx] flex items-center pl-[40rpx]">
          <wd-icon name="search" size="20px" color="#CCCCCC"></wd-icon>
          <input
            type="text"
            placeholder="请输入你的学校"
            v-model.trim="model.educational"
            class="ml-[20rpx] flex-1"
          />
          <view class="w-2rpx h-[60rpx] bg-[#ccc]"></view>
          <text
            class="font4500 ml-[60rpx] pr-[60rpx] text-[28rpx] text-[#999]"
            @click="model.educational = ''"
          >
            清除
          </text>
        </view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">您的所在地</view>
      <view class="mt-[50rpx]">
        <view class="relative">
          <wd-col-picker
            v-model="model.address"
            :columns="area"
            :column-change="columnChange"
            :display-format="displayFormat"
            @confirm="handleAddressConfirm"
            :use-default-slot="true"
          >
            <view
              class="min-h-[99rpx] bg-[#f5f5f5] border-rd-[50rpx] flex pl-[40rpx] pr-[78rpx] items-center"
            >
              <view class="flex-1 font-400 text-[28rpx] text-[#333]">
                {{ model.addressText ? model.addressText : '请选择' }}
              </view>
              <view class="ml-1">
                <wd-icon name="arrow-down" color="#CCCCCC" size="22px"></wd-icon>
              </view>
            </view>
          </wd-col-picker>
        </view>
      </view>
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">您的家乡</view>
      <view class="mt-[68rpx]">
        <view class="relative">
          <wd-col-picker
            v-model="model.home"
            :columns="area"
            :column-change="columnChange"
            @confirm="handleHomeConfirm"
            :display-format="displayFormat"
            :use-default-slot="true"
          >
            <view
              class="min-h-[99rpx] bg-[#f5f5f5] border-rd-[50rpx] flex pl-[40rpx] pr-[78rpx] items-center"
            >
              <view class="flex-1 font-400 text-[28rpx] text-[#333]">
                {{ model.homeText ? model.homeText : '请选择' }}
              </view>
              <view class="ml-1">
                <wd-icon name="arrow-down" color="#CCCCCC" size="22px"></wd-icon>
              </view>
            </view>
          </wd-col-picker>
        </view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">兴趣爱好</view>
      <view class="mt-[50rpx]">
        <view class="bg-[#f5f5f5] border-rd-[50rpx]">
          <wd-textarea
            v-model="model.hobby"
            placeholder="请输入"
            :maxlength="100"
            show-word-limit
          />
        </view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">生活照</view>
      <view class="mt-[50rpx]">
        <view class="album">
          <block v-for="(item, index) in fileList" :key="index">
            <view class="album__item">
              <view class="album__item__img relative" v-if="item.src">
                <wd-img :width="107" :height="132" :src="item.src" :radius="6" enable-preview />
                <view class="absolute top-0 right-0" @click="delImg(item)">
                  <wd-icon name="close-circle-filled" size="22px"></wd-icon>
                </view>
              </view>
              <view class="album__item__inner" v-else @click="addPic(index)">
                <view class="add"><wd-icon name="add" size="18px" color="#312F35"></wd-icon></view>
                <view class="label">{{ item.desc }}</view>
              </view>
            </view>
          </block>
        </view>
        <view class="desc font-400 text-[26rpx] text-[#ccc] text-right mt-[42rpx]">至少三张</view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">您的年收入是？</view>
      <view class="mt-[50rpx]">
        <view class="income">
          <block v-for="(item, index) in incomeList" :key="index">
            <view
              class="income-item"
              :class="item.id === model.annual_income ? 'active' : ''"
              @click="selectIncome(item)"
            >
              {{ item.label }}
            </view>
          </block>
        </view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">
        上传一张好看的照片做头像吧！
      </view>
      <view class="mt-[50rpx] flex flex-col justify-center items-center">
        <view class="upload-avatar">
          <view class="upload-avatar__img relative" v-if="model.avatar">
            <wd-img :width="172" :height="172" :src="model.avatar" :radius="6" enable-preview />
            <view class="absolute top-0 right-0" @click="model.avatar = ''">
              <wd-icon name="close-circle-filled" size="22px"></wd-icon>
            </view>
          </view>
          <view class="upload-avatar__inner" v-else @click="addPic('avatar')">
            <view class="add"><wd-icon name="add" size="18px" color="#312F35"></wd-icon></view>
          </view>
        </view>
        <view class="images">
          <block v-for="(item, index) in imageCaseList" :key="index">
            <view class="images__item">
              <image class="w-[159rpx] h-[174rpx]" :src="item.url" mode="widthFix"></image>
              <view class="images__text flex items-center justify-center">
                {{ item.text }}
                <tetx class="iconfont icon-zhengque ml-[10rpx]" v-if="index === 0"></tetx>
                <tetx class="iconfont icon-cuowu ml-[10rpx]" v-else></tetx>
              </view>
            </view>
          </block>
        </view>
      </view>
    </view>
    <view class="content">
      <view class="font-bold text-[28rpx] text-[#3A3E49] mt-[59rpx]">昵称</view>
      <view class="mt-[50rpx]">
        <view class="h-[99rpx] bg-[#F5F5F5] border-rd-[50rpx] flex items-center pl-[40rpx]">
          <wd-icon name="search" size="20px" color="#CCCCCC"></wd-icon>
          <input
            type="text"
            placeholder="请输入"
            v-model.trim="model.nickname"
            class="ml-[20rpx] flex-1"
          />
          <view class="w-2rpx h-[60rpx] bg-[#ccc]"></view>
          <text class="font4500 ml-[60rpx] pr-[60rpx] text-[28rpx] text-[#999]">清除</text>
        </view>
      </view>
      <view class="submit mt-[553rpx]">
        <view class="mt-[19rpx]">
          <button
            type="button"
            hover-class="button-hover"
            :disabled="!model.nickname"
            class="w-[480rpx] h-[99rpx] border-rd-[50rpx] flex items-center justify-center submit-btn"
            @click="submit"
          >
            提交
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useColPickerData } from '@/hooks/useColPickerData'
import { useToast } from 'wot-design-uni'
import { personalDataSet } from '@/service/user/index'
import { useUserStore } from '@/store/user'
import KeFu from '@/components/KeFu/index.vue'

//
const userStore = useUserStore()
const { onShareAppMessage, onShareTimeline } = useShare()
const toast = useToast()
const selectImgIndex: any = ref(0)
// 上传hooks回调
const uploadCallback = (res) => {
  res = JSON.parse(res)
  if (selectImgIndex.value === 'avatar') {
    model.avatar = res.data.url
  }
  if (selectImgIndex.value >= 0) {
    fileList[selectImgIndex.value].src = res.data.url
  }
  if (selectImgIndex.value === fileList.length - 1) {
    fileList.push({
      src: '',
      desc: '其他',
    })
  }
}
const { runUploadFile } = useUpload({}, uploadCallback)
const { colPickerData, findChildrenByCode } = useColPickerData()
const pageIndex = ref(1)

const minDate = new Date('1924/01/01').getTime()
const model = reactive({
  sex: '',
  birthday: Date.now(),
  educational: '',
  home: [],
  homeText: '',
  home_province_id: '',
  home_city_id: '',
  home_district_id: '',
  address: [],
  province_id: '',
  city_id: '',
  district_id: '',
  school: '',
  addressText: '',
  hobby: '',
  annual_income: -1,
  avatar: '',
  nickname: '',
})

const formatter = (type, value) => {
  switch (type) {
    case 'year':
      return value + '年'
    case 'month':
      return value + '月'
    case 'date':
      return value + '日'
    default:
      return value
  }
}
// 格式化方法
const displayFormat = (selectedItems: Record<string, any>[]) => {
  if (selectedItems.length === 0) return ''
  return selectedItems.map((item) => item.label).join('-')
}

const area = ref<any[]>([
  colPickerData.map((item) => {
    return {
      value: item.value,
      label: item.text,
    }
  }),
])
const columnChange = ({ selectedItem, resolve, finish }) => {
  const areaData = findChildrenByCode(colPickerData, selectedItem.value)
  if (areaData && areaData.length) {
    resolve(
      areaData.map((item) => {
        return {
          value: item.value,
          label: item.text,
        }
      }),
    )
  } else {
    finish()
  }
}

function handleAddressConfirm({ value, selectedItems }) {
  model.addressText = selectedItems.map((item) => item.label).join('-')
  model.province_id = value[0]
  model.city_id = value[1]
  model.district_id = value[2]
  console.log(value)
  console.log('🚀 ~ handleConfirm ~ model:', model)
}
function handleHomeConfirm({ value, selectedItems }) {
  model.homeText = selectedItems.map((item) => item.label).join('-')
  model.home_province_id = value[0]
  model.home_city_id = value[1]
  model.home_district_id = value[2]
  console.log(value)
  console.log('🚀 ~ handleConfirm ~ model:', model)
}

const nextStep = () => {
  pageIndex.value++
  console.log('object :>> 下一步')
  console.log(' pageIndex.value :>> ', pageIndex.value)
}
const handleDateChange = (e: any) => {
  console.log('e :>> ', e)
}

// 计算年龄
const getAge = computed(() => {
  const now = new Date()
  const birth = new Date(model.birthday)
  const age = now.getFullYear() - birth.getFullYear()
  const m = now.getMonth() - birth.getMonth()
  if (m < 0 || (m === 0 && now.getDate() < birth.getDate())) {
    return age - 1
  }
  return age
})
const handleDisable = computed(() => {
  if (model.province_id.length && model.home_city_id.length) {
    return false
  }
  return true
})

// 上传照片
const fileList = reactive<any[]>([
  {
    src: '',
    desc: '生活照',
  },
  {
    src: '',
    desc: '旅行照',
  },
  {
    src: '',
    desc: '其他',
  },
])
//* 选择图片*//
const addPic = (index) => {
  selectImgIndex.value = index
  uni.chooseImage({
    count: 9, // 最多可以选择的图片张数，默认9
    sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
    sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
    success: async (res) => {
      runUploadFile(res.tempFilePaths[0])
    },
  })
}

const delImg = (v) => {
  v.src = ''
}
// 上传图片 下一步按钮是否 禁用
const btnUploadDisabled = computed(() => {
  const imgList = fileList.filter((v) => v.src)
  return imgList.length < 3
})

const incomeList = [
  {
    id: 0,
    label: '保密',
  },
  {
    id: 1,
    label: '5万以下',
  },
  {
    id: 2,
    label: '5-10万',
  },
  {
    id: 3,
    label: '10-20万',
  },
  {
    id: 4,
    label: '20-30万',
  },
  {
    id: 5,
    label: '30-60万',
  },
  {
    id: 6,
    label: '60-100万',
  },
  {
    id: 7,
    label: '100万以上',
  },
]

const selectIncome = (v) => {
  console.log('🚀 ~ selectIncome ~ v:', v)
  model.annual_income = v.id
}
const imageCaseList = [
  {
    url: 'https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/store/comment/c2d8e202506171019457264.png',
    text: '清晰正面',
  },
  {
    url: 'https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/store/comment/1d45f202506171034317409.png',
    text: '侧脸背影',
  },
  {
    url: 'https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/store/comment/65825202506171104576897.png',
    text: '侧脸背影',
  },
  {
    url: 'https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/store/comment/9ea2d202506171103466675.png',
    text: '模糊不清',
  },
]

const submit = async () => {
  toast.loading('请稍后...')
  const lifeImg = fileList
    .filter((item) => item.src)
    .map((item) => item.src)
    .join(',')
  const data = {
    sex: model.sex,
    birthday: model.birthday / 1000,
    educational: model.educational,
    province_id: model.province_id,
    city_id: model.city_id,
    district_id: model.district_id,
    home_province_id: model.home_province_id,
    home_city_id: model.home_city_id,
    home_district_id: model.home_district_id,
    hobby: model.hobby,
    life_img: lifeImg,
    annual_income: model.annual_income,
    avatar: model.avatar,
    nickname: model.nickname,
  }
  const [res, err] = await personalDataSet(data)
  if (res) {
    toast.success('资料设置成功')
    userStore.getUserInfo()
    setTimeout(() => {
      // uni.switchTab({ url: '/pages/index/index' })
      uni.navigateBack()
    }, 2000)
  }
  if (err) {
    toast.close()
  }
}
</script>

<style lang="scss">
//
.custom-class {
  --wot-progress-height: 24rpx;
  --wot-progress-bg: #f1d6d7;
}
.wd-picker-view {
  --wot-picker-column-fs: 48rpx;
  --wot-picker-column-color: #3a3e49;
}

.wd-textarea {
  --wot-textarea-bg: #f5f5f5;
  border-radius: 50rpx;
  --wot-textarea-cell-padding: 38rpx;
  --wot-textarea-padding: 40rpx;
}
.wd-textarea__inner {
  height: 200rpx !important;
}
.submit-btn {
  font-size: 30rpx;
  font-weight: 400;
  color: #ffffff;
  background: linear-gradient(90deg, #ff94b2, #f9225e);
}
.submit-btn[disabled] {
  font-size: 30rpx;
  font-weight: 400;
  color: #ffffff;
  background: #f1d6d7;
}
.wd-picker-view__roller {
  padding: 20rpx 0 !important;
}
.wd-picker-view-column__item {
  display: flex;
  align-items: center;
}
// .wd-col-picker__cell {
//   opacity: 0;
// }
.album {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  row-gap: 20rpx;
  column-gap: 20rpx;
  &__item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 215rpx;
    height: 265rpx;
    background: #f5f5f5;
    border-radius: 20rpx;
    &__inner {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      .add {
        display: flex;
        align-items: center;
        justify-content: center;

        width: 56rpx;
        height: 56rpx;
        background: #ffffff;
        border-radius: 50%;
      }
      .label {
        margin-top: 20rpx;
        font-size: 26rpx;
        font-weight: 400;
        color: #312f35;
      }
    }
  }
}
.income {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40rpx;
  &-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 60rpx;
    font-size: 30rpx;
    font-weight: 400;
    color: #666666;
    border: 1px solid #cccccc;
    border-radius: 30rpx;
    transition: all 0.3s;
  }
  .active {
    color: #ffffff !important;
    background: #ff94b2 !important;
    border: none;
  }
}
.upload-avatar {
  &__inner {
    display: flex;
    flex-direction: center;
    align-items: center;
    justify-content: center;
    width: 345rpx;
    height: 345rpx;
    background: #f5f5f5;
    border-radius: 49rpx;
    .add {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56rpx;
      height: 56rpx;
      background: #ffffff;
      border-radius: 50%;
    }
  }
}
.images {
  display: flex;
  gap: 6rpx;
  margin-top: 60rpx;
  .images__text {
    font-size: 25rpx;
    font-weight: 400;
    color: #312f35;
    text-align: center;
  }
}
.icon-zhengque {
  color: #0fcf13;
}
.icon-cuowu {
  color: #d71800;
}
</style>
