<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '我的特权',
  },
}
</route>

<template>
  <view class="user-card-container">
    <KeFu />

    <view class="relative z-10">
      <!-- 状态切换tabs -->
      <view class="status-tabs">
        <view
          class="tab-item"
          :class="{ active: currentTab === tab.value }"
          v-for="tab in tabs"
          :key="tab.value"
          @click="handleTabChange(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>

      <!-- 卡片列表 -->
      <view class="card-list">
        <view class="card-item" v-for="card in list" :key="card.id">
          <view class="card-status" :class="{ invalid: card.is_valid !== 1 }">
            {{ card.is_valid === 1 ? '使用中' : '已失效' }}
          </view>

          <view class="card-content">
            <view class="card-title">
              {{ card.title }}
              <image class="card-icon" :src="card.image" mode="widthFix" />
            </view>

            <!-- 次数卡信息 -->
            <template v-if="card.type === 2">
              <view class="info-row">
                <text class="label">剩余次数：</text>
                <text class="value highlight">{{ card.cika_num_has }}/{{ card.cika_num }}次</text>
              </view>
            </template>

            <!-- 时效卡信息 -->
            <template v-else>
              <view class="info-row">
                <text class="label">有效期：</text>
                <text class="value" v-if="card.shixiao_num === 0">永久有效</text>
                <text class="value" v-else>{{ card.shixiao_start }} 至 {{ card.shixiao_end }}</text>
              </view>
            </template>

            <view class="info-row time">
              <text class="label">开通时间：</text>
              <text class="value">{{ card.add_time }}</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="list.length === 0" class="empty-state">暂无相关会员卡</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { userCardList } from '@/service/user'
import KeFu from '@/components/KeFu/index.vue'
//
// 状态tabs配置
const tabs = [
  { label: '全部', value: '' },
  { label: '使用中', value: '1' },
  { label: '已失效', value: '2' },
]

const currentTab = ref('')
interface MembershipCard {
  id: number
  uid: number
  lists_id: number
  types_id: number
  type: number // 类型 1时效卡 2次数卡
  title: string // 会员卡名称
  intr: string
  image: string // 显示图标
  price: string
  cika_num: number // 次数卡 - 总次数
  cika_num_has: number // 次数卡 - 剩余次数
  shixiao_num: number // 时效卡 - 月数, 如果为0表示永久
  shixiao_start: string // 时效卡 - 生效时间
  shixiao_end: string // 时效卡 - 截止时间, 如果shixiao_num=0表示永久
  is_valid: number // 是否有效 1有效 2无效
  sort: number
  is_del: number
  add_time: string
}
const list = ref<MembershipCard[]>([])

const handleTabChange = (value: string) => {
  currentTab.value = value
  getList()
}
const getList = async () => {
  const [res, err] = await userCardList({ is_valid: currentTab.value, page: 1, limit: 100 })
  if (err) return
  if (res) {
    list.value = res.data.list
  }
  console.log(res)
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.user-card-container {
  position: relative;
  min-height: 100vh;
  padding: 28rpx;
  background-color: #f3f2fd;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 442rpx;
    content: '';
    background: linear-gradient(180deg, #e8e2ff 0%, #f3f2fd 100%);
  }
}

.status-tabs {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-around;
  padding: 10rpx;
  margin-bottom: 30rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx -4rpx rgba(145, 158, 171, 0.1);

  .tab-item {
    position: relative;
    padding: 16rpx 40rpx;
    font-size: 28rpx;
    color: #666;
    border-radius: 12rpx;
    transition: all 0.3s ease;

    &.active {
      font-weight: 500;
      color: #f9225e;
      background: rgba(137, 102, 255, 0.1);

      &::after {
        position: absolute;
        bottom: 6rpx;
        left: 50%;
        width: 20rpx;
        height: 3rpx;
        content: '';
        background: #f9225e;
        border-radius: 4rpx;
        transform: translateX(-50%);
      }
    }

    &:active {
      opacity: 0.8;
    }
  }
}

.card-list {
  padding: 0 10rpx;

  .card-item {
    position: relative;
    padding: 28rpx;
    margin-bottom: 30rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f7ff 100%);
    border: 1rpx solid rgba(137, 102, 255, 0.1);
    border-radius: 24rpx;
    box-shadow:
      0 4rpx 24rpx -8rpx rgba(137, 102, 255, 0.15),
      0 4rpx 8rpx -4rpx rgba(137, 102, 255, 0.1);
    transition: all 0.3s ease;

    &:active {
      box-shadow:
        0 2rpx 12rpx -4rpx rgba(137, 102, 255, 0.15),
        0 2rpx 4rpx -2rpx rgba(137, 102, 255, 0.1);
      transform: scale(0.98);
    }

    .card-icon {
      z-index: 1;
      width: 120rpx;
      height: 28rpx;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 8rpx -2rpx rgba(0, 0, 0, 0.1);
    }

    .card-content {
      position: relative;
    }

    .card-title {
      padding-right: 120rpx;
      margin-bottom: 24rpx;
      font-size: 34rpx;
      font-weight: 600;
      line-height: 1.4;
      color: #333;
    }

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      font-size: 28rpx;
      line-height: 1.5;

      &.time {
        padding-top: 20rpx;
        margin-top: 24rpx;
        margin-bottom: 0;
        font-size: 26rpx;
        color: #999;
        border-top: 1rpx solid rgba(137, 102, 255, 0.08);
      }

      .label {
        min-width: 140rpx;
        color: #666;
      }

      .value {
        flex: 1;
        color: #333;

        &.highlight {
          font-size: 34rpx;
          font-weight: 600;
          color: #f9225e;
          background: linear-gradient(90deg, #f9225e, #ff94b2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .card-status {
      position: absolute;
      top: 40rpx;
      right: 30rpx;
      padding: 6rpx 24rpx;
      font-size: 24rpx;
      color: #fff;
      background: linear-gradient(90deg, #f9225e, #ff94b2);
      border-radius: 24rpx;
      box-shadow: 0 4rpx 8rpx -2rpx rgba(137, 102, 255, 0.3);

      &.invalid {
        background: linear-gradient(90deg, #999, #bbb);
        box-shadow: 0 4rpx 8rpx -2rpx rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.empty-state {
  padding: 60rpx 0;
  font-size: 28rpx;
  color: #999;
  text-align: center;
}
</style>
