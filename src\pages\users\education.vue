<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '学历认证',
    navigationBarBackgroundColor: '#03E0EE',
    navigationBarTextStyle: 'white',
  },
}
</route>

<template>
  <view class="">
    <KeFu />
    <view class="banner bg-[#03E0EE] pt-[10rpx] px-[30rpx] pb-[30rpx]">
      <view class="flex items-center justify-between mt-[45rpx]">
        <view class="">
          <view class="font-bold text-[40rpx] text-[#fff] mb-[20rpx]">学历认证</view>
          <view class="font-400 text-[26rpx] line-height-[30rpx] text-[#fff]">完成学历认证</view>
          <view class="font-400 text-[26rpx] line-height-[30rpx] text-[#fff]">
            认识更多和你一样优秀的他
          </view>
        </view>
        <view class="pr-[20rpx]">
          <image
            class="w-[154rpx] h-[152rpx]"
            src="/static/images/icon-xuelirenzheng.png"
            mode="widthFix"
          ></image>
        </view>
      </view>
    </view>
    <view class="py-[60rpx] px-[30rpx]">
      <view class="h-[99rpx] bg-[#F5F5F5] border-rd-[50rpx] flex items-center pl-[40rpx]">
        <input type="text" placeholder="请输入姓名" class="ml-[20rpx] flex-1" />
      </view>
      <view
        class="h-[99rpx] bg-[#F5F5F5] border-rd-[50rpx] flex items-center pl-[40rpx] mt-[40rpx]"
      >
        <input type="text" placeholder="请输入身份证号" class="ml-[20rpx] flex-1" />
      </view>
      <view class="submit mt-[339rpx]">
        <button
          type="button"
          hover-class="button-hover"
          class="w-[480rpx] h-[99rpx] border-rd-[50rpx] flex items-center justify-center submit-btn"
          @click="submit"
        >
          立即认证
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import KeFu from '@/components/KeFu/index.vue'
//
const { onShareAppMessage, onShareTimeline } = useShare()
const submit = () => {
  console.log('object :>> ')
  uni.switchTab({ url: '/pages/index/index' })
}
</script>

<style lang="scss" scoped>
//
.submit-btn {
  font-size: 30rpx;
  font-weight: 400;
  color: #ffffff;
  background: #03e0ee;
}
</style>
