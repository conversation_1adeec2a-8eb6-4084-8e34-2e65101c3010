<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '活动列表',
  },
}
</route>

<template>
  <view class="">
    <KeFu />
    <view class="py-2 px-3">
      <view class="ul">
        <view class="bg-white border-rd-[19rpx] p-[19rpx] p-r-[33rpx]">
          <view class="flex items-center justify-between">
            <view class="relative">
              <image
                class="w-[200rpx] h-[200rpx] border-rd-[32rpx]"
                src="https://fakeimg.pl/100x100/3c9cff/fff"
                mode="widthFix"
              ></image>
              <view class="tag-success">通过审核</view>
              <!-- <view class="tag-over">已结束</view> -->
            </view>
            <view class="ml-3">
              <view class="text-[30rpx ] text-[#333] font-bold">
                露营踏青，桌游飞盘，享受初夏的凉爽
              </view>
              <view class="flex items-center mt-[32rpx]">
                <text class="iconfont icon-shijian text-[#666666] mr-[10rpx]"></text>
                <text class="text-[26rpx] text-[#666666]">8月26日 周一</text>
              </view>
              <view class="flex items-center mt-[21rpx]">
                <text class="iconfont icon-dizhi1 text-[#666666] mr-[10rpx]"></text>
                <view class="ellipsis text-[26rpx] text-[#666666] w-[350rpx]">
                  四川省成都市龙泉驿区三圣乡 四川省成都市龙泉驿区三圣乡 四川省成都市龙泉驿区三圣乡
                  四川省成都市龙泉驿区三圣乡
                </view>
              </view>
            </view>
          </view>
          <view class="">
            <button type="button" hover-class="button-hover" class="btn">去缴费</button>
          </view>
        </view>
        <view class="bg-white border-rd-[19rpx] p-[19rpx] p-r-[33rpx] mt-[20rpx]">
          <view class="flex items-center justify-between">
            <view class="relative">
              <image
                class="w-[200rpx] h-[200rpx] border-rd-[32rpx]"
                src="https://fakeimg.pl/100x100/3c9cff/fff"
                mode="widthFix"
              ></image>
              <view class="tag-success">未通过审核</view>
              <!-- <view class="tag-over">已结束</view> -->
            </view>
            <view class="ml-3">
              <view class="text-[30rpx ] text-[#333] font-bold">
                露营踏青，桌游飞盘，享受初夏的凉爽
              </view>
              <view class="flex items-center mt-[32rpx]">
                <text class="iconfont icon-shijian text-[#666666] mr-[10rpx]"></text>
                <text class="text-[26rpx] text-[#666666]">8月26日 周一</text>
              </view>
              <view class="flex items-center mt-[21rpx]">
                <text class="iconfont icon-dizhi1 text-[#666666] mr-[10rpx]"></text>
                <view class="ellipsis text-[26rpx] text-[#666666] w-[350rpx]">
                  四川省成都市龙泉驿区三圣乡 四川省成都市龙泉驿区三圣乡 四川省成都市龙泉驿区三圣乡
                  四川省成都市龙泉驿区三圣乡
                </view>
              </view>
            </view>
          </view>
          <view class="">
            <button type="button" hover-class="button-hover" class="btn">去修改</button>
          </view>
        </view>
        <view class="bg-white border-rd-[19rpx] p-[19rpx] p-r-[33rpx] mt-[20rpx]">
          <view class="flex items-center justify-between">
            <view class="relative">
              <image
                class="w-[200rpx] h-[200rpx] border-rd-[32rpx]"
                src="https://fakeimg.pl/100x100/3c9cff/fff"
                mode="widthFix"
              ></image>
              <view class="tag-success">未通过审核</view>
              <!-- <view class="tag-over">已结束</view> -->
            </view>
            <view class="ml-3">
              <view class="text-[30rpx ] text-[#333] font-bold">
                露营踏青，桌游飞盘，享受初夏的凉爽
              </view>
              <view class="flex items-center mt-[32rpx]">
                <text class="iconfont icon-shijian text-[#666666] mr-[10rpx]"></text>
                <text class="text-[26rpx] text-[#666666]">8月26日 周一</text>
              </view>
              <view class="flex items-center mt-[21rpx]">
                <text class="iconfont icon-dizhi1 text-[#666666] mr-[10rpx]"></text>
                <view class="ellipsis text-[26rpx] text-[#666666] w-[350rpx]">
                  四川省成都市龙泉驿区三圣乡 四川省成都市龙泉驿区三圣乡 四川省成都市龙泉驿区三圣乡
                  四川省成都市龙泉驿区三圣乡
                </view>
              </view>
            </view>
          </view>
          <view class="">
            <button type="button" hover-class="button-hover" class="btn">去修改</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import KeFu from '@/components/KeFu/index.vue'
//
const { onShareAppMessage, onShareTimeline } = useShare()
</script>

<style lang="scss">
//
page {
  background-color: #f2f2f2;
}

.tag-success {
  position: absolute;
  top: 14rpx;
  left: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 77rpx;
  padding: 4rpx 10rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #ff94b2;
  background: #312f35;
  border-radius: 14rpx;
  opacity: 0.55;
}
.tag-over {
  position: absolute;
  top: 14rpx;
  left: 14rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 77rpx;
  height: 38rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #cccccc;
  background: #312f35;
  border-radius: 13rpx;
  opacity: 0.55;
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 298rpx;
  height: 64rpx;
  padding: 0;
  margin: 0;
  margin-top: 18rpx;
  margin-left: auto;
  font-size: 30rpx;
  font-weight: 400;
  color: #ffffff;
  background: #333333;
  border-radius: 32rpx;
}
</style>
