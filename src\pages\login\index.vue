<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '三择一生',
    navigationBarBackgroundColor: '#FF94B2',
  },
}
</route>

<template>
  <view class="overflow-hidden w-full h-full flex flex-col items-center justify-center">
    <image
      class="w-[525rpx] h-[491rpx] mt-[148rpx]"
      src="https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/store/comment/4c505202506171033218321.png"
      mode="widthFix"
    ></image>
    <view class="mt-35rpx font-400 text-[37rpx] text-[#333]">三择一生</view>
    <!-- <button
      hover-class="button-hover"
      class="w-[480rpx] h-[99rpx] border-rd-[50rpx] mt-[158rpx] font-400 text-[30rpx] text-[#F6C5C8] bg-[#4B4948] flex items-center justify-center"
      open-type="getPhoneNumber"
      @getphonenumber="getPhoneNumber"
    >
      微信快捷登陆
    </button> -->
    <button
      hover-class="button-hover"
      class="w-[480rpx] h-[99rpx] border-rd-[50rpx] mt-[158rpx] font-400 text-[30rpx] text-[#F6C5C8] bg-[#4B4948] flex items-center justify-center"
      @click="onLogin"
    >
      微信快捷登陆
    </button>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
//
const { onShareAppMessage, onShareTimeline } = useShare()

const userStore = useUserStore()
const onLogin = () => {
  userStore.login()
  uni.navigateTo({
    url: '/pages/login/information',
  })
}
const getPhoneNumber = async (e) => {
  const { code, iv, encryptedData } = e.detail
  await userStore.loginAuthBindingPhone({ code, iv, encryptedData })
  // console.log('object :>> 登录完成')
  // console.log('🚀 ~ getPhoneNumber ~ code:', code)
}
</script>

<style lang="scss">
//
page {
  background: linear-gradient(180deg, #ff94b2, #f9225e);
}
</style>
