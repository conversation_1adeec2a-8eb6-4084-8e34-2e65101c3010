<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
  },
}
</route>

<template>
  <view class="p-2 bg-white">
    <KeFu />
    <mp-html :content="content"></mp-html>
  </view>
</template>

<script lang="ts" setup>
import KeFu from '@/components/KeFu/index.vue'
import { getAgreement } from '@/service/public/index'
//
const content = ref('')
onLoad(async ({ id, title }) => {
  uni.setNavigationBarTitle({ title })
  const [res, err] = await getAgreement(id)
  if (res) {
    content.value = res.data.content
  }
})
</script>

<style lang="scss" scoped>
//
</style>
