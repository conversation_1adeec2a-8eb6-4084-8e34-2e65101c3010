<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '活动详情',
  },
}
</route>

<template>
  <view class="wrapper pb-[30rpx]">
    <KeFu />

    <view class="card-swiper">
      <wd-swiper
        :list="activityInfo.image_input"
        autoplay
        :height="375"
        v-model:current="current"
        @click="handleClick"
        @change="onChange"
      >
        <template #indicator="{ current, total }">
          <view class="custom-indicator">{{ current + 1 }}/{{ total }}</view>
        </template>
      </wd-swiper>
    </view>
    <!--活动详情 -->
    <view class="px-5">
      <view class="info">
        <view class="tags px-4">
          <view class="tag" v-for="(item, index) in activityInfo.biaoqian" :key="index">
            {{ item }}
          </view>
        </view>
        <view class="head px-4">
          <view class="title">{{ activityInfo.title }}</view>
          <!-- <view class="subtitle">
            <text class="text-[#F00707] text-[36rpx] font-bold">¥{{ activityInfo.price }}</text>
          </view> -->
          <view class="share">
            <button
              type="button"
              class="p-0 m-0 bg-transparent"
              open-type="share"
              hover-class="none"
            >
              <wd-img src="/static/images/icon-share.png" width="32" height="32" mode="widthFix" />
            </button>
          </view>
        </view>
        <view class="details px-4">
          <view class="row">
            <view class="label">
              <wd-img src="/static/images/icon-time.png" width="16" height="16" mode="widthFix" />
            </view>
            <view class="text">{{ activityInfo.start_time }}</view>
          </view>
          <view class="row">
            <view class="label">
              <wd-img
                src="/static/images/icon-address.png"
                width="16"
                height="16"
                mode="widthFix"
              />
            </view>
            <view class="text">{{ activityInfo.address }}</view>
          </view>
          <view class="row">
            <view class="label">
              <wd-img src="/static/images/icon-people.png" width="16" height="16" mode="widthFix" />
            </view>
            <view class="text">限{{ activityInfo.num }}人</view>
          </view>
          <view class="price">¥ {{ activityInfo.price }}</view>
        </view>
        <view class="line">
          <image src="/static/images/icon-line.png" mode="widthFix" class="w-full h-[28rpx] my-5" />
        </view>
        <view class="content">
          <mp-html :content="activityInfo.content" />
        </view>
        <view class="info__title mt-[40rpx] px-4">活动须知</view>
        <view class="activity-rules px-4">
          <view class="activity-rules__title">
            <image
              class="w-[22rpx] h-[30rpx] mr-2"
              src="/static/images/icon-guize.png"
              mode="widthFix"
            ></image>
            报名审核
          </view>
          <view class="activity-rules__info">
            <view class="p">
              付款成功≠报名成功，支付完成以后，请耐心等待，活动开始前1-3天开始审核，您可以在"三择一生"小程序中"已参与"查看报名状态。
              若未能报名成功，费用将原路退还至您的微信。
            </view>
            <view class="p">
              若报名成功，请进入活动群，群内通知活动时间地点。无论报名成功与否，您都会收到短信和小程序通知推送。
            </view>
          </view>
        </view>
        <view class="activity-rules px-4">
          <view class="activity-rules__title">
            <image
              class="w-[28rpx] h-[30rpx] mr-2"
              src="/static/images/icon-guize.png"
              mode="widthFix"
            ></image>
            取消退款
          </view>
          <view class="activity-rules__info">
            <view class="p">
              报名审核通过后，距离活动开始时间:48小时之前取消，可退全部费用。
              距离活动开始48小时之内取消，不退还报名费用。
            </view>
            <view class="p">本次活动报名费不可转让，不可顺延至以后活动使用。</view>
          </view>
        </view>
      </view>
    </view>
    <view class="footer px-[30rpx] p-t-2 pd-ios">
      <view class="num">
        <!-- 111 -->
      </view>
      <view class="btns">
        <!-- <button type="button" class="btn btn-join" hover-class="none">已报10人</button> -->
        <button
          type="button"
          class="btn btn-submit"
          hover-class="button-hover"
          @click="onClickJoin"
        >
          报名
        </button>
      </view>
    </view>

    <!-- 添加邀请人信息弹窗 -->
    <wd-popup
      v-model="showInviterPopup"
      custom-style="border-radius: 30rpx;"
      :close-on-click-modal="false"
    >
      <view class="inviter-popup">
        <view class="close-icon" @click="showInviterPopup = false">
          <wd-icon name="close" size="20px" color="#999"></wd-icon>
        </view>
        <view class="popup-title">填写邀请人信息</view>
        <view class="popup-content">
          <wd-input
            v-model="inviterForm.inviter"
            label="邀请人"
            required
            placeholder="请输入邀请人"
            class="input-item"
          />
          <wd-input
            v-model="inviterForm.phone"
            label="手机号"
            type="number"
            placeholder="请输入手机号(选填)"
            class="input-item"
            :maxlength="11"
          />
        </view>
        <view class="popup-footer">
          <button class="submit-btn" hover-class="button-hover" @click="handleInviterSubmit">
            确认
          </button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { activityDetail, joinActivity, pay } from '@/service/user/index'
import { useToast, useMessage } from 'wot-design-uni'

import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import KeFu from '@/components/KeFu/index.vue'

//
const userStore = useUserStore()
const { isComplete } = storeToRefs(userStore)

const { onShareAppMessage, onShareTimeline, setSharePath } = useShare()

const message = useMessage()
const toast = useToast()
const current = ref<number>(0)
const activityInfo = ref({
  id: '',
  image_input: [],
  title: '',
  content: '',
  start_time: '',
  address: '',
  price: 0,
  biaoqian: [],
  num: 0,
})

// 添加邀请人表单相关的响应式数据
const showInviterPopup = ref(false)
const inviterForm = reactive({
  inviter: '',
  phone: userStore.userInfo.phone,
})

function handleClick(e) {
  console.log(e)
}
function onChange(e) {
  // console.log(e)
}
const loadDetail = async (id) => {
  const [res, err] = await activityDetail(id)
  if (res) {
    activityInfo.value = res.data
    setSharePath(`pages/activity/activity-detail?id=${activityInfo.value.id}`)
  }
}
onLoad(({ id }) => {
  loadDetail(id)
})
const handlePay = async (id) => {
  const [res, err] = await pay(id)
  if (res) {
    const jsConfig = res.data.result.jsConfig
    console.log('🚀 ~ handlePay ~ jsConfig:', jsConfig)
    uni.requestPayment({
      provider: 'alipay',
      orderInfo: 'orderInfo',
      timeStamp: jsConfig.timestamp,
      nonceStr: jsConfig.nonceStr,
      package: jsConfig.package,
      signType: jsConfig.signType,
      paySign: jsConfig.paySign,
      success: () => {
        toast.close()
        toast.success('支付成功')
        // 延时两秒
        setTimeout(() => {
          uni.setStorageSync('isPay', true)
          uni.setStorageSync('isPayPage', true)
          uni.switchTab({
            url: '/pages/participated/index',
          })
        }, 2000)
      },
      fail: (e) => {
        console.log('e :>> ', e)
        toast.close()
        toast.warning('取消支付')
        setTimeout(() => {
          uni.setStorageSync('isPayPage', true)
          uni.switchTab({
            url: '/pages/participated/index',
          })
        }, 2000)
      },
    })
  }
}
// 报名
const onClickJoin = async () => {
  if (!isComplete.value) {
    return message
      .confirm({
        msg: '您的资料不完整，暂时无法享受全部功能',
        title: '系统提醒',
        confirmButtonText: '立即完善',
        cancelButtonText: '稍后再说',
      })
      .then(() => {
        uni.navigateTo({ url: '/pages/login/information' })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
  }

  // 显示邀请人信息填写弹窗
  showInviterPopup.value = true
}

// 处理邀请人信息提交
const handleInviterSubmit = async () => {
  if (!inviterForm.inviter) {
    return toast.warning('请填写邀请人姓名')
  }
  // 如果手机号不为空，则验证手机号是否正确
  if (inviterForm.phone && inviterForm.phone.length !== 11) {
    return toast.warning('请填写正确的手机号')
  }

  // 关闭弹窗
  showInviterPopup.value = false

  // 调用报名接口,添加邀请人信息
  const [res, err] = await joinActivity(activityInfo.value.id, {
    inviter: inviterForm.inviter,
    phone: inviterForm.phone ? inviterForm.phone : userStore.userInfo.phone,
  })

  if (res) {
    toast.show(res.msg)
    if (res.data.is_pay === 0) {
      handlePay(res.data.id)
    }
  }
}
</script>

<style>
page {
  background-color: #f2f2f2;
}
</style>
<style lang="scss" scoped>
//
.custom-indicator {
  position: absolute;
  right: 333rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 49rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
}

.card-swiper {
  --wot-swiper-radius: 0;
}

.info {
  box-sizing: border-box;
  width: 100%;
  padding: 38rpx 0;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;
  transform: translateY(-138rpx);

  .tags {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 22rpx;
    margin-bottom: 50rpx;

    .tag {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 26rpx;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border-radius: 11rpx;
      border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }

  .head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 52rpx;

    .title {
      flex: 1;
      font-size: 30rpx;
      font-weight: 500;
      color: #303030;
    }

    .subtitle {
      margin-left: 16rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #7a7a7a;
    }
  }

  .details {
    position: relative;

    .price {
      position: absolute;
      right: 40rpx;
      bottom: 0;
      font-size: 39rpx;
      font-weight: 400;
      color: #303030;
      background: linear-gradient(-31deg, #ff94b2 0%, #f9225e 100%);
      -webkit-background-clip: text;
      /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent;
      /*给文字设置成透明*/
    }

    .row {
      display: flex;
      // align-items: center;
      margin-bottom: 26rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #303030;

      .label {
        flex-shrink: 0;
        margin-right: 16rpx;
      }

      .text {
        flex: 1;
      }
    }
  }

  .content {
    margin-top: 40rpx;
  }

  &__title {
    font-size: 30rpx;
    font-weight: 400;
    color: #333333;
  }

  &__row {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #666666;

    &__label {
      width: 100rpx;
      color: #999999;
    }
  }
}

.activity-rules {
  margin-top: 20rpx;

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 26rpx;
    font-weight: 400;
    color: #999999;
  }

  .p {
    font-size: 26rpx;
    font-weight: 400;
    line-height: 40rpx;
    color: #999999;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: #fff;
  box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

  .btns {
    display: flex;
    align-items: center;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60rpx;
      padding: 0;
      margin: 0;
      font-size: 30rpx;
      font-weight: 400;
      color: #ffffff;
      border-radius: 34rpx;
    }

    .btn-join {
      width: 177rpx;
      margin-right: 20rpx;
      color: #f9225e;
      background: linear-gradient(24deg, #f5efef, #f5efef, #f4efef);
      border-radius: 30rpx;
      border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }

    .btn-submit {
      width: 258rpx;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border-radius: 30rpx;
      border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }
}

.inviter-popup {
  position: relative;
  width: 600rpx;
  padding: 60rpx 40rpx 40rpx;
  background: #fff;
  border-radius: 30rpx;

  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    padding: 10rpx;
    cursor: pointer;
  }

  .popup-title {
    margin-bottom: 50rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  .popup-content {
    margin-bottom: 50rpx;

    .input-item {
      margin-bottom: 30rpx;

      :deep(.wd-input) {
        padding: 16rpx 24rpx;
        background: #f8f8f8;
        border-radius: 16rpx;
        transition: all 0.3s ease;
      }

      :deep(.wd-input__label) {
        font-weight: 500;
        color: #333;
      }

      :deep(.wd-input__inner) {
        height: 88rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        background: transparent;
        border: none;
        border-radius: 8rpx;
        transition: all 0.3s ease;
      }
    }
  }

  .popup-footer {
    margin-top: 50rpx;

    .submit-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      font-size: 32rpx;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border: none;
      border-radius: 44rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }
}
</style>
