<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '活动',
    //开启下拉刷新
    enablePullDownRefresh: true,
    //页面上拉触底事件触发时距页面底部距离，单位只支持px
    onReachBottomDistance: 100,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="wrapper bg-white">
    <CustomNavbar title="活 动" />
    <KeFu />
    <view class="content">
      <TopCheckAddress
        placeholder="活动名称"
        @cityChange="handleCityChange"
        @search="handleSearch"
      />
      <view class="tab">
        <view class="tab__item">
          <button
            type="button"
            hover-class="button-hover"
            class="btn"
            :class="{ active: current === 0 }"
            @click="handleSwitch(0)"
          >
            <image
              :src="
                current === 0
                  ? '/static/images/icon-huoodng.png'
                  : '/static/images/icon-huoodng-active.png'
              "
              class="w-[37rpx] h-[37rpx] mr-[28rpx]"
              mode="widthFix"
            />
            报名中
          </button>
        </view>
        <view class="tab__item">
          <button
            type="button"
            hover-class="button-hover"
            class="btn"
            :class="{ active: current === 1 }"
            @click="handleSwitch(1)"
          >
            <image
              :src="
                current === 1
                  ? '/static/images/icon-huodong-2.png'
                  : '/static/images/icon-huodong-2-active.png'
              "
              class="w-[54rpx] h-[40rpx] mr-[28rpx]"
              mode="widthFix"
            />
            我参与的
          </button>
        </view>
      </view>
      <view v-show="current === 0">
        <view class="activity-list mt-6">
          <block v-for="item in self.activityList" :key="item.id">
            <view class="activity-item" @click="toActivityDetail(item.id)">
              <view class="image-wrapper">
                <image class="activity-image" :src="item.image_input[0]" mode="aspectFill"></image>
              </view>
              <view class="info-wrapper">
                <view class="title">{{ item.title }}</view>
                <view class="time-info">
                  <text class="iconfont icon-shijian icon"></text>
                  <text class="text">{{ item.start_time_date }} {{ item.start_time_week }}</text>
                </view>
                <view class="address-info">
                  <text class="iconfont icon-dizhi2 icon"></text>
                  <view class="text ellipsis">{{ item.address }}</view>
                </view>
              </view>
              <!-- 操作区域 -->
              <view class="operation">
                <view>
                  <button type="button" hover-class="button-hover" class="btn">报 名</button>
                </view>
                <view class="number">人数:{{ item.num }}人</view>
              </view>
            </view>
          </block>
        </view>
        <view class="history-title">
          <view class="line"></view>
          往期活动
          <view class="line"></view>
        </view>
        <view class="activity-list">
          <block v-for="item in self.expiredActivity" :key="item.id">
            <view class="activity-item history" @click="toActivityDetail(item.id)">
              <view class="image-wrapper">
                <image class="activity-image" :src="item.image_input[0]" mode="aspectFill"></image>
              </view>
              <view class="info-wrapper">
                <view class="title">{{ item.title }}</view>
                <view class="time-info">
                  <text class="iconfont icon-shijian icon"></text>
                  <text class="text">{{ item.start_time_date }} {{ item.start_time_week }}</text>
                </view>
                <view class="address-info">
                  <text class="iconfont icon-dizhi2 icon"></text>
                  <view class="text ellipsis">{{ item.address }}</view>
                </view>
              </view>
              <!-- 操作区域 -->
              <view class="operation">
                <view>
                  <button type="button" hover-class="button-hover" class="btn no">已结束</button>
                </view>
                <view class="number">人数:{{ item.num }}人</view>
              </view>
            </view>
          </block>
          <!-- 暂无更多 -->
          <wd-loadmore :state="pageData.state" loading-text="加载中..." finished-text="暂无更多" />
        </view>
      </view>
      <view v-show="current === 1">
        <!-- 展示活动状态 -->
        <view class="activity-status-box">
          <block v-for="(item, index) in activityStatus" :key="index">
            <view
              class="activity-status-item"
              :class="{ active: self.status === item.value }"
              @click="handleActivitySwitch(item)"
            >
              {{ item.label }}
            </view>
          </block>
        </view>
        <view class="activity-list mt-6">
          <block v-for="item in userActivityList" :key="item.id">
            <view class="activity-item" @click="toActivityDetail(item.id)">
              <view class="image-wrapper">
                <image
                  class="activity-image"
                  :src="item.article.image_input[0]"
                  mode="aspectFill"
                ></image>
              </view>
              <view class="info-wrapper">
                <view class="title">
                  <text class="status-box" :class="getActivityClass(item.status)">
                    【{{ getActivitStatus(item.status) }}】
                  </text>
                  {{ item.article.title }}
                </view>
                <view class="time-info">
                  <text class="iconfont icon-shijian icon"></text>
                  <text class="text">
                    {{ item.article.start_time_date }} {{ item.article.start_time_week }}
                  </text>
                </view>
                <view class="address-info">
                  <text class="iconfont icon-dizhi2 icon"></text>
                  <view class="text ellipsis">{{ item.article.address }}</view>
                </view>
              </view>
              <!-- 操作区域 -->
              <view class="operation">
                <view v-if="item.is_pay === 0">
                  <button
                    type="button"
                    hover-class="button-hover"
                    class="btn"
                    @click.stop="handlePay(item.id)"
                  >
                    立即支付
                  </button>
                </view>
                <view>
                  <button
                    type="button"
                    hover-class="button-hover"
                    class="btn btn-2"
                    @click.stop="handleCancel(item.id)"
                    v-if="item.is_can_cancel === 1"
                  >
                    取消
                  </button>
                </view>
                <view>
                  <button
                    type="button"
                    hover-class="button-hover"
                    class="btn btn-2"
                    v-if="item.is_can_delete === 1"
                    @click.stop="handleDel(item.id)"
                  >
                    删除
                  </button>
                </view>
                <view class="price" :class="{ over: item.status >= 3 }">¥ {{ item.price }}</view>
              </view>
            </view>
          </block>
          <wd-loadmore
            :state="loading ? 'loading' : 'finished'"
            loading-text="加载中..."
            finished-text="暂无更多"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { activityList, myCancelJoin, myDelJoin, myActivityList, pay } from '@/service/user/index'
import { useToast, useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import CustomNavbar from '@/components/CustomNavbar/index.vue'
import TopCheckAddress from '@/components/TopCheckAddress/index.vue'
import { useRequestPaging } from '@/hooks/useRequestPaging'
import KeFu from '@/components/KeFu/index.vue'

//
const { isComplete } = storeToRefs(useUserStore())
const { onShareAppMessage, onShareTimeline } = useShare()
const message = useMessage()
const toast = useToast()

// tab
const current = ref(0)

const handleSwitch = (index) => {
  current.value = index
}
// 活动状态
const activityStatus = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '待支付',
    value: '0',
  },
  {
    label: '已支付',
    value: '1',
  },
  {
    label: '已退款',
    value: '2',
  },
]

onLoad(() => {
  if (!isComplete.value) {
    message
      .confirm({
        msg: '您的资料不完整，暂时无法享受全部功能',
        title: '系统提醒',
        confirmButtonText: '立即完善',
        cancelButtonText: '稍后再说',
      })
      .then(() => {
        uni.navigateTo({ url: '/pages/login/information' })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
  }
})
// 监听滚动高度
onPageScroll((res) => {
  uni.$emit('onPageScroll', res.scrollTop)
})

const self = reactive<{
  activityList: any[] // 未过期
  expiredActivity: any[] // 过期
  userList: any[]
  current: number
  status: string
}>({
  activityList: [],
  expiredActivity: [],
  userList: [],
  current: 0,
  status: '',
})
//
const pageData = reactive({
  page: 1,
  limit: 10,
  loading: false,
  loadend: false,
  state: 'loading' as 'loading' | 'finished',
  cityId: '',
  title: '',
})
const reset = () => {
  pageData.page = 1
  pageData.loadend = false
  pageData.loading = false
  self.activityList = []
  self.expiredActivity = []
  loadActiveList()
  loadExpiredActivity()
}

const handleCityChange = (e) => {
  pageData.cityId = e
  reset()
}
const handleSearch = (e) => {
  pageData.title = e
  reset()
}

const loadActiveList = async () => {
  const [res, err] = await activityList({
    type: 1,
    page: 1,
    title: pageData.title,
    city_id: pageData.cityId,
    limit: 99,
  })
  if (res) {
    console.log('res :>> ', res)
    self.activityList = res.data.list
  }
}

const loadExpiredActivity = async () => {
  if (pageData.loadend) return
  pageData.loading = true

  const [res, err] = await activityList({
    type: 2,
    page: pageData.page,
    title: pageData.title,
    city_id: pageData.cityId,
    limit: 10,
  })
  if (res) {
    const list = res.data.list
    const loadend = list.length < pageData.limit
    pageData.loadend = loadend
    pageData.page++
    pageData.state = loadend ? 'finished' : 'loading'
    self.expiredActivity = [...self.expiredActivity, ...list]
  }
  pageData.loading = false
}
loadActiveList()
loadExpiredActivity()

// 去活动详情
const toActivityDetail = (id) => {
  uni.navigateTo({
    url: '/pages/activity/activity-detail?id=' + id,
  })
}
//
const {
  data: userActivityList,
  loading,
  loadMore,
  resetParams,
} = useRequestPaging({
  apiFunction: myActivityList,
  initialParams: () => {
    return {
      is_pay: self.status,
    }
  },
})
// -----------
const handleActivitySwitch = (v) => {
  self.status = v.value
  resetParams()
}
//
const getActivitStatus = (status) => {
  switch (status) {
    case 0:
      return '待支付'
    case 1:
      return '审核成功'
    case 2:
      return '审核中'
    case 3:
      return '审核失败'
    case 4:
      return '用户取消'
    default:
      return ''
  }
}
// 活动状态样式
const getActivityClass = (status) => {
  switch (status) {
    case 0:
    case 2:
    case 3:
      return 'status-2'
    case 1:
      return 'status-1'
    case 4:
      return 'status-3'
    default:
      return ''
  }
}
const handleDel = async (id) => {
  message
    .confirm({
      msg: '是否删除当前活动',
      title: '系统提示',
    })
    .then(async () => {
      const [res, err] = await myDelJoin(id)
      if (res) {
        toast.success('删除成功')
        resetParams()
      }
    })
    .catch(() => {
      console.log('点击了取消按钮')
    })
}
//
const handleCancel = async (id) => {
  message
    .confirm({
      msg: '是否取消当前活动',
      title: '系统提示',
    })
    .then(async () => {
      const [res, err] = await myCancelJoin(id)
      if (res) {
        toast.success('取消成功')
        resetParams()
      }
    })
    .catch(() => {
      console.log('点击了取消按钮')
    })
}
//
const handlePay = async (id) => {
  const [res, err] = await pay(id)
  if (res) {
    const jsConfig = res.data.result.jsConfig
    uni.requestPayment({
      provider: 'alipay',
      orderInfo: 'orderInfo',
      timeStamp: jsConfig.timestamp,
      nonceStr: jsConfig.nonceStr,
      package: jsConfig.package,
      signType: jsConfig.signType,
      paySign: jsConfig.paySign,
      success: () => {
        toast.close()
        toast.success('支付成功')
        // 延时两秒
        setTimeout(() => {
          resetParams()
        }, 2000)
      },
      fail: (e) => {
        console.log('e :>> ', e)
        toast.close()
        toast.warning('取消支付')
      },
    })
  }
}
//
// 监听下拉刷新
onPullDownRefresh(() => {
  if (current.value === 0) {
    pageData.page = 1
    pageData.loadend = false
    pageData.loading = false
    loadActiveList()
    loadExpiredActivity()
  } else {
    resetParams()
  }
  setTimeout(() => {
    // 关闭下拉刷新
    uni.stopPullDownRefresh()
  }, 1000)
})
//
onReachBottom(() => {
  if (current.value === 1) {
    loadMore()
  } else {
    loadExpiredActivity()
  }
})
</script>

<style lang="scss" scoped>
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-top: 20rpx;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 442rpx;
    content: '';
    background: url('https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/attach/2025/08/d9ce220250804112831395.png')
      no-repeat;
    background-size: 100% 100%;
  }
}

.content {
  position: relative;
  z-index: 10;
  padding: 0 28rpx 28rpx;
}

.tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 40rpx;

  &__item {
    flex: 1;

    &:last-child {
      margin-left: 62rpx;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 71rpx;
      font-size: 28rpx;
      font-weight: normal;
      color: #f9225e;
      border: 1rpx solid #fb225e;
      border-radius: 36rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }

    .active {
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border: none;
    }
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18rpx;
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  }
}

.image-wrapper {
  position: relative;

  .activity-image {
    width: 180rpx;
    height: 180rpx;
    border-radius: 20rpx;
  }
}

.info-wrapper {
  flex: 1;
  margin-left: 18rpx;

  .title {
    font-size: 26rpx;
    font-weight: 500;
    color: #303030;

    .status-box {
      font-size: 20rpx;
      color: #303030;
    }

    .status-1 {
      color: #f9225e;
    }

    .status-2 {
      color: #e84510;
    }

    .status-3 {
      color: #767676;
    }
  }

  .time-info,
  .address-info {
    display: flex;
    align-items: center;
    margin-top: 20rpx;

    .icon {
      margin-right: 10rpx;
      color: #ff225e;
    }

    .text {
      font-size: 26rpx;
      color: #303030;
    }
  }

  .address-info {
    margin-top: 21rpx;

    .text {
      max-width: 200rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.operation {
  margin-left: 46rpx;

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 104rpx;
    height: 57rpx;
    padding: 0 10rpx;
    font-size: 20rpx;
    color: #ffffff;
    background: linear-gradient(-31deg, #ff94b2, #f9225e);
    border-radius: 29rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  }

  .no {
    color: #7f7f7f;
    background: linear-gradient(-31deg, #f5efef, #f5efef, #f4efef);
  }

  .btn-2 {
    margin-top: 18rpx;
    color: #303030;
    background: linear-gradient(-31deg, #f5efef, #f5efef, #f4efef) !important;
  }

  .number {
    margin-top: 80rpx;
    font-size: 26rpx;
    font-weight: 500;
    color: #f9225e;
  }

  .price {
    margin-top: 18rpx;
    font-size: 28rpx;
    color: #9a6bff;
  }

  .over {
    color: #7f7f7f !important;
  }
}

.history-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 40rpx 0;
  font-size: 28rpx;
  color: #7f7f7f;

  .line {
    width: 263rpx;
    height: 1rpx;
    background: #a0a0a3;
  }
}

// 历史活动项的特殊样式
.activity-item.history {
  .title,
  .icon,
  .text,
  .number {
    color: #ccc;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-status-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-top: 64rpx;

  .activity-status-item {
    padding: 8rpx 30rpx;
    font-size: 26rpx;
    color: #303030;
    border-radius: 36rpx;
    transition: all 0.3s;
  }

  .active {
    color: #ffffff;
    background: linear-gradient(24deg, #ff94b2, #f9225e);
    border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  }
}
</style>
