<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '会员特权',
  },
}
</route>

<template>
  <view class="vip-card-container">
    <!-- 顶部服务说明 -->
    <KeFu />
    <view class="relative z-10">
      <view class="service-intro">
        <view class="service-title">择偶定制服务 - 职场精英智性恋套餐</view>
        <view class="service-icons">
          <view class="icon-item">
            <image class="icon" mode="aspectFit" src="/static/images/vip/diamond.png" />
            <text>优质资源</text>
          </view>
          <view class="icon-item">
            <image class="icon" mode="aspectFit" src="/static/images/vip/message.png" />
            <text>情感咨询</text>
          </view>
          <view class="icon-item">
            <image class="icon" mode="aspectFit" src="/static/images/vip/ticket.png" />
            <text>尊享优惠</text>
          </view>
          <view class="icon-item">
            <image class="icon" mode="aspectFit" src="/static/images/vip/like.png" />
            <text>筛选推荐</text>
          </view>
        </view>
      </view>

      <!-- 分类标签 -->
      <view class="category-tabs">
        <view
          class="tab"
          :class="{ active: index === activeShixiaoIndex }"
          v-for="(item, index) in cardListInfo?.shixiao"
          :key="item.id"
          @click="switchShixiaoTab(index)"
        >
          {{ item.title }}
        </view>
      </view>
      <!-- 会员卡列表 -->
      <view class="card-list">
        <view
          class="card-item"
          :class="{ active: card.id === selectedShixiaoCardId }"
          v-for="card in currentShixiaoCards"
          :key="card.id"
          @click="selectShixiaoCard(card.id)"
        >
          <view class="card-title">
            <text>{{ card.title }}</text>
          </view>
          <view class="card-price-duration">
            <text class="card-price">¥{{ card.price }}</text>
            <text class="card-duration">/{{ card.shixiao_num }}个月</text>
          </view>
        </view>
      </view>

      <!-- 底部导航 -->
      <view class="category-tabs">
        <view
          class="tab"
          :class="{ active: index === activeCikaIndex }"
          v-for="(item, index) in cardListInfo?.cika"
          :key="item.id"
          @click="switchCikaTab(index)"
        >
          {{ item.title }}
        </view>
      </view>

      <!-- 会员卡列表 -->
      <view class="card-list">
        <view
          class="card-item"
          :class="{ active: card.id === selectedCikaCardId }"
          v-for="card in currentCikaCards"
          :key="card.id"
          @click="selectCikaCard(card.id)"
        >
          <view class="card-title">
            <text>{{ card.title }}</text>
          </view>
          <view class="card-price-duration">
            <text class="card-price">¥{{ card.price }}</text>
            <text class="card-duration">/{{ card.cika_num }}次</text>
          </view>
        </view>
      </view>
      <!-- 底部充值确认 -->
      <view class="bottom-action pd-ios">
        <view class="price-cardListInfo">
          <text>充值金额：</text>
          <text class="amount" v-if="selectedCardcardListInfo">
            ¥{{ selectedCardcardListInfo.price }}
          </text>
          <text class="duration" v-if="selectedCardcardListInfo">
            /{{ selectedCardcardListInfo.duration }}
          </text>
          <text v-else>请选择会员卡</text>
        </view>
        <view class="btn">
          <button
            class="confirm-btn center"
            :disabled="!selectedCardcardListInfo"
            @click="handleConfirm"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getCardList, postCardPay } from '@/service/public'
import { useToast } from 'wot-design-uni'
import KeFu from '@/components/KeFu/index.vue'
//
const toast = useToast()
interface Card {
  id: number
  title: string // 卡名称
  image: string // 卡图片
  price: string // 价格
}

interface ShixiaoCard extends Card {
  shixiao_num: number // 购买后有效月数
}

interface CikaCard extends Card {
  cika_num: number // 购买后可使用次数
}

interface CardCategory<T extends Card> {
  id: number
  title: string // 类别名称
  childs: T[] // 会员卡列表
}

interface CardData {
  shixiao: CardCategory<ShixiaoCard>[] // 时效卡列表
  cika: CardCategory<CikaCard>[] // 次数卡列表
}
const cardListInfo = ref<CardData>({
  shixiao: [],
  cika: [],
})
// 加载数据
const loadData = async () => {
  const [res, err] = await getCardList({})
  if (err) {
    return
  }
  cardListInfo.value = res.data
}
loadData()

// 当前选中的分类索引
const activeShixiaoIndex = ref(0)
const activeCikaIndex = ref(0)

// 当前选中的卡片ID
const selectedShixiaoCardId = ref<number | null>(null)
const selectedCikaCardId = ref<number | null>(null)

// 当前显示的数据
const currentShixiaoCards = computed(
  () => cardListInfo.value?.shixiao[activeShixiaoIndex.value]?.childs || [],
)
const currentCikaCards = computed(
  () => cardListInfo.value?.cika[activeCikaIndex.value]?.childs || [],
)

// 切换分类的方法
const switchShixiaoTab = (index: number) => {
  activeShixiaoIndex.value = index
  selectedShixiaoCardId.value = null // 切换分类时清除选中状态
}

const switchCikaTab = (index: number) => {
  activeCikaIndex.value = index
  selectedCikaCardId.value = null // 切换分类时清除选中状态
}

// 选择卡片的方法
const selectShixiaoCard = (cardId: number) => {
  selectedShixiaoCardId.value = cardId
  selectedCikaCardId.value = null // 选择时效卡时清除次卡选中状态
}

const selectCikaCard = (cardId: number) => {
  selectedCikaCardId.value = cardId
  selectedShixiaoCardId.value = null // 选择次卡时清除时效卡选中状态
}

// 计算选中卡片的信息用于显示在底部
const selectedCardcardListInfo = computed(() => {
  if (selectedShixiaoCardId.value) {
    const card = currentShixiaoCards.value.find((c) => c.id === selectedShixiaoCardId.value)
    console.log(card)
    return card ? { id: card.id, price: card.price, duration: `${card.shixiao_num}个月` } : null
  }
  if (selectedCikaCardId.value) {
    const card = currentCikaCards.value.find((c) => c.id === selectedCikaCardId.value)
    console.log(card)
    return card ? { id: card.id, price: card.price, duration: `${card.cika_num}次` } : null
  }
  return null
})

const handlePay = async (jsConfig) => {
  console.log('🚀 ~ handlePay ~ jsConfig:', jsConfig)
  uni.requestPayment({
    provider: 'alipay',
    orderInfo: 'orderInfo',
    timeStamp: jsConfig.timestamp,
    nonceStr: jsConfig.nonceStr,
    package: jsConfig.package,
    signType: jsConfig.signType,
    paySign: jsConfig.paySign,
    success: () => {
      toast.success({
        msg: '支付成功',
        closed() {
          uni.redirectTo({
            url: '/pages/users/vip_card/user_card',
          })
        },
      })
    },
    fail: (e) => {
      toast.warning({
        msg: '取消支付',
      })
    },
  })
}
const handleConfirm = async () => {
  console.log('确定')
  // 调用支付
  const [res, err] = await postCardPay(selectedCardcardListInfo.value.id.toString())
  if (err) {
    return
  }
  const jsConfig = res.data.result.jsConfig
  handlePay(jsConfig)
}
</script>

<style lang="scss" scoped>
.vip-card-container {
  position: relative;
  min-height: 100vh;
  padding: 28rpx;
  padding-bottom: 120rpx;
  background-color: #f3f2fd;
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 442rpx;
    content: '';
    // 渐变从左上角到右下角
    background: url('https://sanzeyisheng.oss-cn-beijing.aliyuncs.com/attach/2025/08/d9ce220250804112831395.png')
      no-repeat;
    background-size: 100% 100%;
  }
}

.service-intro {
  padding: 30rpx;
  color: white;
  background: url('https://cdn.sanzeyisheng.cdhuyun.cn/store/comment/5e08620250226111513617.png')
    no-repeat;
  background-size: 100% 100%;
  border-radius: 20rpx;

  .service-title {
    margin-bottom: 30rpx;
    font-size: 28rpx;
    text-align: center;
  }

  .service-icons {
    display: flex;
    justify-content: space-between;

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .icon {
        width: 70rpx;
        height: 70rpx;
        margin-bottom: 10rpx;
      }

      text {
        font-size: 24rpx;
        color: #fff;
      }
    }
  }
}

.category-tabs {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;

  .tab {
    padding: 8rpx 40rpx;
    font-size: 24rpx;
    color: #303030;
    border-radius: 30rpx;

    &.active {
      color: #fff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
    }
  }
}

.card-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  row-gap: 26rpx;
  column-gap: 30rpx;
  padding: 30rpx 26rpx;
  margin-top: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
  .card-item {
    padding: 16rpx;
    background: #f6f5fe;
    border: 1rpx solid #f9225e;
    border-radius: 12rpx;
    &.active {
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      .card-title {
        color: #fff;
      }
      .card-price-duration {
        color: #fff;
      }
    }

    .card-title {
      margin-bottom: 20rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #303030;
    }
    .card-price-duration {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      color: #ff94b2;
    }

    .card-price {
      font-size: 30rpx;
      font-weight: bold;
    }

    .card-duration {
      font-size: 26rpx;
    }
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .price-cardListInfo {
    font-size: 26rpx;
    .amount {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff94b2;
    }

    .duration {
      font-size: 24rpx;
      color: #999;
    }
  }

  .confirm-btn {
    width: 148rpx;
    height: 57rpx;
    font-size: 26rpx;
    color: #fff;
    background: #ff94b2;
    border-radius: 28rpx;

    &[disabled] {
      background: #ccc;
      opacity: 0.6;
    }
  }
}
</style>
