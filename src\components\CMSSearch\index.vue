<template>
  <view class="box">
    <!-- <view class="address-box">
      <wd-picker :columns="cityColumns" v-model="city" use-default-slot @confirm="handleConfirm">
        <view class="address">
          <text class="address__text">{{ city }}</text>
          <wd-icon name="arrow-down" size="32rpx" color="#6F6F6F"></wd-icon>
        </view>
      </wd-picker>
    </view> -->
    <view class="input-box">
      <image src="/static/images/icon-search.png" mode="widthFix" class="w-[25rpx] h-[25rpx]" />
      <input
        class="input"
        v-model="keyWord"
        :placeholder="props.placeholder"
        placeholder-class="input-placeholder"
      />
      <view class="search-text" @click="onSearch">搜索</view>
    </view>
    <!-- <view class="message-box">
      <image src="/static/images/icon-message.png" mode="widthFix" class="w-[60rpx] h-[60rpx]" />
    </view> -->
  </view>
</template>

<script lang="ts" setup>
import { getOpenCity } from '@/service/public'
//
const props = defineProps<{
  placeholder: string
}>()

const emit = defineEmits(['cityChange', 'search'])
const keyWord = ref('')
const cityData = ref([])
const cityColumns = ref(['全部'])
const city = ref('全部')
const handleConfirm = (e) => {
  console.log('🚀 ~ handleConfirm ~ e:', e)
  city.value = e.value
  const findItem = cityData.value.find((item) => item.name === e.value)
  if (!findItem) {
    emit('cityChange', '')
    return
  }
  emit('cityChange', findItem.city_id)
}
const loadCity = async () => {
  const [res, err] = await getOpenCity()
  if (res) {
    cityData.value = res.data
    cityColumns.value = ['全部', ...res.data.map((item) => item.name)]
    console.log(res)
  }
}
loadCity()

const onSearch = () => {
  emit('search', keyWord.value)
}
</script>

<style lang="scss" scoped>
//
.box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .address {
    display: flex;
    align-items: center;
    margin-right: 20rpx;
    &__text {
      margin-right: 10rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #000000;
    }
  }
  .input-box {
    display: flex;
    flex: 1;
    align-items: center;
    height: 64rpx;
    padding: 18rpx 24rpx;
    margin-right: 20rpx;
    background: #ffffff;
    border-radius: 32rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.13);
    .input {
      width: 100%;
      margin-left: 10rpx;
    }
    .search-text {
      position: relative;
      padding-left: 10rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #ff225e;
      &::after {
        position: absolute;
        top: 50%;
        left: 0;
        display: block;
        width: 3rpx;
        height: 80%;
        content: '';
        background: linear-gradient(-31deg, #ff94b2, #f9225e);
        transform: translateY(-50%);
      }
    }
  }
  .message-box {
    margin-right: 18rpx;
  }
}
:deep(.input-placeholder) {
  font-size: 25rpx;
  font-weight: 400;
  color: #aaaaaa;
}
</style>
