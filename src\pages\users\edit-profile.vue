<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '编辑资料',
  },
}
</route>

<template>
  <view class="wrapper">
    <!-- <view class="avatar__inner" @click="addPic"> -->
    <KeFu />
    <view class="avatar__inner">
      <!--  -->
      <button
        open-type="chooseAvatar"
        @chooseavatar="onChooseAvatar"
        class="avatar-btn"
        hover-class="none"
      >
        <wd-img :width="95" :height="95" :src="form.avatar" round />
      </button>
      <view class="icon">
        <text class="iconfont icon-xiangji"></text>
      </view>
    </view>
    <view class="ul">
      <view class="input__inner">
        <view class="label">昵称</view>
        <view class="info">
          <view class="content">
            <input type="nickname" class="input" v-model="form.nickname" placeholder="请填写" />
          </view>
          <wd-icon name="arrow-right" size="22px" color="#D8D8D8"></wd-icon>
        </view>
      </view>
      <view class="input__inner">
        <view class="label">手机号</view>
        <view class="info">
          <view class="content">
            <input type="text" class="input" v-model="form.phone" placeholder="请填写" />
          </view>
          <wd-icon name="arrow-right" size="22px" color="#D8D8D8"></wd-icon>
        </view>
      </view>
      <!-- <view class="input__inner">
        <view class="label">微信号</view>
        <view class="info">
          <view class="content">
            <input type="text" class="input" v-model="form.wechat_code" placeholder="请填写" />
          </view>
          <wd-icon name="arrow-right" size="22px" color="#D8D8D8"></wd-icon>
        </view>
      </view> -->
      <wd-datetime-picker
        type="date"
        v-model="form.birthday"
        label="年月日"
        :use-default-slot="true"
        :minDate="minDate"
      >
        <view class="input__inner">
          <view class="label">生日</view>
          <view class="info">
            <view class="content">{{ getBirthday }}</view>
            <wd-icon name="arrow-right" size="22px" color="#D8D8D8"></wd-icon>
          </view>
        </view>
      </wd-datetime-picker>
    </view>
    <view class="submit mt-[206rpx]">
      <button
        type="button"
        hover-class="button-hover"
        class="w-[480rpx] h-[99rpx] border-rd-[50rpx] flex items-center justify-center submit-btn"
        @click="submit"
      >
        保存
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { personalDataEdit } from '@/service/user/index'
import { useToast } from 'wot-design-uni'
import KeFu from '@/components/KeFu/index.vue'
//
const { onShareAppMessage, onShareTimeline } = useShare()
const uploadCallback = (res) => {
  res = JSON.parse(res)
  if (res.data.url) {
    form.avatar = res.data.url
  }
}
const { runUploadFile } = useUpload({}, uploadCallback)

const toast = useToast()
const userStore = useUserStore()

const form = reactive({
  nickname: '',
  birthday: 0,
  avatar: '',
  phone: '',
  wechat_code: '',
})
const minDate = new Date('1924/01/01').getTime()
//* 选择图片*//
const addPic = (index) => {
  uni.chooseImage({
    count: 9, // 最多可以选择的图片张数，默认9
    sizeType: ['compressed'], // original 原图，compressed 压缩图，默认二者都有
    sourceType: ['album', 'camera'], // album 从相册选图，camera 使用相机，默认二者都有
    success: async (res) => {
      runUploadFile(res.tempFilePaths[0])
    },
  })
}
const onChooseAvatar = (e) => {
  const { avatarUrl } = e.detail
  runUploadFile(avatarUrl)
}
// 获取生日
const getBirthday = computed(() => {
  // 时间戳转 yy/dd/mm
  if (!form.birthday) return ''
  const date = new Date(form.birthday) // 时间戳为10位需*1000，时间戳为13位的话不需乘1000
  const Y = date.getFullYear() + '-'
  const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
  const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  return Y + M + D
})
onLoad(() => {
  form.nickname = userStore.userInfo.nickname
  form.phone = userStore.userInfo.phone
  form.wechat_code = userStore.userInfo.wechat_code
  form.birthday = userStore.userInfo.birthday * 1000
  form.avatar = userStore.userInfo.avatar
  console.log(form)
})
const submit = async () => {
  if (!form.avatar) return toast.error('请上传头像')
  if (!form.nickname) return toast.error('请填写昵称')
  if (!form.phone) return toast.error('请填写手机号')
  if (!/^1[3456789]\d{9}$/.test(form.phone)) return toast.error('请填写正确的手机号')
  // if (!form.wechat_code) return toast.error('请填写微信号')
  if (!form.birthday) return toast.error('请选择生日')
  // 验证手机号
  const [res, err] = await personalDataEdit({ ...form, birthday: form.birthday / 1000 })
  if (res) {
    toast.success('资料设置成功')
    userStore.getUserInfo()
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
}
</script>

<style lang="scss" scoped>
//
page {
  background-color: #fff;
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
}

.avatar__inner {
  position: relative;
  border: 4rpx solid #fff;
  border-radius: 50%;
  .icon {
    position: absolute;
    top: 136rpx;
    left: 135rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 54rpx;
    height: 54rpx;
    background: #333333;
    border-radius: 50%;
    .iconfont {
      font-size: 34rpx;
      color: #fff;
    }
  }
}
.ul {
  width: 100%;
  margin-top: 40rpx;
}
.input__inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  margin-bottom: 10rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  .label {
    min-width: 150rpx;
    font-size: 30rpx;
    font-weight: 400;
    color: #666666;
  }
  .info {
    display: flex;
    align-items: center;
  }
  .content {
    flex: 1;
    margin-right: 20rpx;
    font-size: 30rpx;
    font-weight: 400;
    color: #333333;
    .input {
      width: 100%;
      text-align: right;
    }
  }
}
.submit-btn {
  font-size: 30rpx;
  font-weight: 400;
  color: #ffffff;
  background: linear-gradient(90deg, #ff94b2, #f9225e);
}
.avatar-btn {
  background-color: transparent !important;
}
</style>
