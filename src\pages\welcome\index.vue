<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    backgroundColor: '#000000',
  },
}
</route>

<template>
  <view class="page flex-col">
    <view class="block_1 flex-col justify-between">
      <view class="box_1 flex-col">
        <view class="group_1 flex-row">
          <image
            class="image_3"
            referrerpolicy="no-referrer"
            src="/static/images/welcome-man.png"
          />
          <view class="image-text_1 flex-row">
            <view class="text-group_1 flex-row">
              <text class="text_1">奔爱</text>
            </view>
          </view>
          <image
            class="image_3"
            referrerpolicy="no-referrer"
            src="/static/images/welcome-woman.png"
          />
          <view class="image-text_1 flex-row">
            <view class="text-group_1 flex-row">
              <text class="text_2">与你约法三章</text>
            </view>
          </view>
        </view>
        <view class="group_2 flex-col justify-between">
          <view class="block_2 flex-col">
            <text class="text_3">我处于单身状态，我无男女朋友关系</text>
            <view class="image-text_2 flex-row justify-between">
              <image
                class="label_1"
                referrerpolicy="no-referrer"
                src="/static/images/welcome-choose.png"
              />
              <text class="text-group_2">我是单身状态</text>
            </view>
          </view>
          <view class="block_2 flex-col">
            <text class="text_3">我无赌博等不良嗜好，非失信人</text>
            <view class="image-text_2 flex-row justify-between">
              <image
                class="label_1"
                referrerpolicy="no-referrer"
                src="/static/images/welcome-choose.png"
              />
              <text class="text-group_2">我无不良嗜好</text>
            </view>
          </view>
        </view>

        <view class="group_4 flex-col">
          <view class="paragraph_1">
            <view><text class="paragraph_txt">我坚守以下脱单价值观</text></view>
            <view><text class="paragraph_txt">· 尊重彼此爱情观</text></view>
            <view><text class="paragraph_txt">· 坚持男女价值观平等</text></view>
            <view><text class="paragraph_txt">· 打击抵制不良行为</text></view>
            <view><text class="paragraph_txt">· 捍卫多元理想爱情</text></view>
          </view>
          <view class="image-text_4 flex-row justify-between">
            <view class="group_5 flex-col">
              <image
                class="label_1"
                referrerpolicy="no-referrer"
                src="/static/images/welcome-choose.png"
              />
            </view>
            <view class="text-group_4">
              <text class="text_5">遵守价值观及</text>
              <text class="text_6" @click="toPage(5)">《用户协议》</text>
              <text class="text_6" @click="toPage(4)">《隐私协议》</text>
            </view>
          </view>
        </view>
        <view class="text-wrapper_1 flex-col" @click="navigateToHome">
          <text class="text_7">进入奔爱</text>
        </view>
      </view>
    </view>
    <!-- 跳过按钮 -->
    <!-- <view class="skip-btn" @click="skipSplash">跳过 {{ countdown }}s</view> -->
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const countdown = ref(5)
let timer: any

// 跳过开屏
const skipSplash = () => {
  clearInterval(timer)
  navigateToHome()
}

// 跳转到首页
const navigateToHome = () => {
  uni.switchTab({ url: '/pages/index/index' })
}

onMounted(async () => {
  // 启动倒计时
  // timer = setInterval(() => {
  //   countdown.value--
  //   if (countdown.value <= 0) {
  //     clearInterval(timer)
  //     navigateToHome()
  //   }
  // }, 1000)

  // 预加载用户信息
  await userStore.login().catch(() => {})
})

const toPage = (status) => {
  switch (status) {
    case 1:
      uni.navigateTo({ url: '/pages/users/profile' })
      break
    case 2:
      uni.navigateTo({ url: '/pages/login/realname' })
      break
    case 3:
      uni.navigateTo({ url: '/pages/users/education' })
      break
    case 4:
      uni.navigateTo({ url: '/pages/users/agreement?title=隐私协议&id=3' })
      break
    case 5:
      uni.navigateTo({ url: '/pages/users/agreement?title=用户协议&id=4' })
      break
    case 7:
      uni.navigateTo({ url: '/pages/message/index' })
      break
  }
}

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style lang="scss" scoped>
page view {
  box-sizing: border-box;
  flex-shrink: 0;
}
button {
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: 1px solid transparent;
  outline: none;
}

button:active {
  opacity: 0.6;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.justify-start {
  display: flex;
  justify-content: flex-start;
}
.justify-center {
  display: flex;
  justify-content: center;
}

.justify-end {
  display: flex;
  justify-content: flex-end;
}
.justify-evenly {
  display: flex;
  justify-content: space-evenly;
}
.justify-around {
  display: flex;
  justify-content: space-around;
}
.justify-between {
  display: flex;
  justify-content: space-between;
}
.align-start {
  display: flex;
  align-items: flex-start;
}
.align-center {
  display: flex;
  align-items: center;
}
.align-end {
  display: flex;
  align-items: flex-end;
}

.page {
  // position: relative;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  .block_1 {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    .image-wrapper_1 {
      width: 100%;
      height: 128rpx;
      background-size: 100% 100%;
      .image_1 {
        width: 727rpx;
        height: 20rpx;
        margin: 11rpx 0 0 13rpx;
      }
      .image_2 {
        width: 193rpx;
        height: 78rpx;
        margin: 18rpx 0 2rpx 544rpx;
      }
    }
    .box_1 {
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      .group_1 {
        height: 81rpx;
        margin: 12rpx 0 0 29rpx;
        .image_3 {
          width: 45rpx;
          height: 75rpx;
          margin-top: 3rpx;
        }
        .image-text_1 {
          height: 81rpx;
          .text-group_1 {
            height: 39rpx;
            margin-top: 11rpx;
            .text_1 {
              width: 77rpx;
              height: 38rpx;
              margin-top: 1rpx;
              font-family: SourceHanSansSC-Bold;
              font-size: 40rpx;
              font-weight: 700;
              line-height: 40rpx;
              color: rgba(58, 62, 73, 1);
              text-align: left;
              overflow-wrap: break-word;
              white-space: nowrap;
              background-image: linear-gradient(
                rgba(249, 34, 94, 1) 0,
                rgba(249, 34, 94, 1) 0.415039%,
                rgba(255, 148, 178, 1) 100%,
                rgba(255, 148, 178, 1) 100%
              );
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .text_2 {
              width: 237rpx;
              height: 39rpx;
              font-family: SourceHanSansSC-Bold;
              font-size: 40rpx;
              font-weight: 700;
              line-height: 40rpx;
              color: rgba(58, 62, 73, 1);
              text-align: left;
              overflow-wrap: break-word;
              white-space: nowrap;
              background-image: linear-gradient(
                rgba(249, 34, 94, 1) 0,
                rgba(249, 34, 94, 1) 0.415039%,
                rgba(255, 148, 178, 1) 100%,
                rgba(255, 148, 178, 1) 100%
              );
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .image_4 {
            width: 53rpx;
            height: 81rpx;
            margin: 0 247rpx 0 -300rpx;
          }
        }
      }
      .group_2 {
        width: 690rpx;
        height: 376rpx;
        margin: 38rpx 0 0 30rpx;
        .block_2 {
          justify-content: flex-center;
          width: 690rpx;
          height: 168rpx;
          background-color: rgba(255, 246, 249, 1);
          border-radius: 49rpx;
          .text_3 {
            width: 478rpx;
            height: 30rpx;
            margin: 40rpx 0 0 38rpx;
            font-family: SourceHanSansSC-Normal;
            font-size: 30rpx;
            font-weight: normal;
            line-height: 30rpx;
            color: rgba(51, 51, 51, 1);
            text-align: left;
            overflow-wrap: break-word;
            white-space: nowrap;
          }
          .image-text_2 {
            width: 233rpx;
            height: 36rpx;
            margin: 22rpx 0 40rpx 39rpx;
            .label_1 {
              width: 36rpx;
              height: 36rpx;
            }
            .text-group_2 {
              width: 178rpx;
              height: 29rpx;
              margin-top: 3rpx;
              font-family: SourceHanSansSC-Normal;
              font-size: 30rpx;
              font-weight: normal;
              line-height: 30rpx;
              color: rgba(51, 51, 51, 1);
              text-align: left;
              overflow-wrap: break-word;
              white-space: nowrap;
            }
          }
        }
        .block_3 {
          justify-content: flex-center;
          width: 690rpx;
          height: 168rpx;
          margin-top: 40rpx;
          background-color: rgba(255, 246, 249, 1);
          border-radius: 49rpx;
          .text_4 {
            width: 418rpx;
            height: 30rpx;
            margin: 40rpx 0 0 38rpx;
            font-family: SourceHanSansSC-Normal;
            font-size: 30rpx;
            font-weight: normal;
            line-height: 30rpx;
            color: rgba(51, 51, 51, 1);
            text-align: left;
            overflow-wrap: break-word;
            white-space: nowrap;
          }
          .image-text_3 {
            width: 233rpx;
            height: 36rpx;
            margin: 22rpx 0 40rpx 39rpx;
          }
        }
      }
      .group_4 {
        justify-content: flex-center;
        width: 690rpx;
        // height: 408rpx;
        margin: 40rpx 0 0 30rpx;
        background-color: rgba(255, 246, 249, 1);
        border-radius: 49rpx;
        .paragraph_1 {
          margin: 40rpx 0 0 38rpx;
          .paragraph_txt {
            height: 81rpx;
            font-family: SourceHanSansSC-Normal;
            font-size: 30rpx;
            font-weight: normal;
            line-height: 60rpx;
            color: rgba(51, 51, 51, 1);
            text-align: left;
            overflow-wrap: break-word;
          }
        }
        .image-text_4 {
          width: 578rpx;
          height: 36rpx;
          margin: 39rpx 0 40rpx 39rpx;
          .group_5 {
            width: 36rpx;
            height: 36rpx;
            background-size: 100% 100%;
          }
          .text-group_4 {
            width: 523rpx;
            height: 29rpx;
            margin-top: 3rpx;
            font-family: SourceHanSansSC-Normal;
            font-size: 0;
            font-weight: normal;
            line-height: 30rpx;
            text-align: left;
            overflow-wrap: break-word;
            white-space: nowrap;
            .text_5 {
              width: 523rpx;
              height: 29rpx;
              font-family: SourceHanSansSC-Normal;
              font-size: 30rpx;
              font-weight: normal;
              line-height: 30rpx;
              color: rgba(51, 51, 51, 1);
              text-align: left;
              overflow-wrap: break-word;
              white-space: nowrap;
            }
            .text_6 {
              width: 523rpx;
              height: 29rpx;
              font-family: SourceHanSansSC-Normal;
              font-size: 30rpx;
              font-weight: normal;
              line-height: 30rpx;
              color: rgba(250, 47, 104, 1);
              text-align: left;
              overflow-wrap: break-word;
              white-space: nowrap;
            }
          }
        }
      }
      .text-wrapper_1 {
        width: 480rpx;
        height: 99rpx;
        margin: 52rpx 0 100rpx 135rpx;
        background: url('/static/images/welcome-bt.png') 100% no-repeat;
        background-size: 100% 100%;
        .text_7 {
          width: 118rpx;
          height: 29rpx;
          margin: 35rpx 0 0 181rpx;
          font-family: SourceHanSansSC-Normal;
          font-size: 30rpx;
          font-weight: normal;
          line-height: 30rpx;
          color: rgba(255, 255, 255, 1);
          text-align: left;
          overflow-wrap: break-word;
          white-space: nowrap;
        }
      }
    }
  }
}

.skip-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 999;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: white;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 30rpx;
}
</style>
