<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    backgroundColor: '#000000',
  },
}
</route>

<template>
  <view class="splash-container">
    <!-- 开屏图 -->
    <image class="splash-image" src="/static/images/splash.png"></image>
    <!-- 跳过按钮 -->
    <view class="skip-btn" @click="skipSplash">跳过 {{ countdown }}s</view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()
const countdown = ref(5)
let timer: any

// 跳过开屏
const skipSplash = () => {
  clearInterval(timer)
  navigateToHome()
}

// 跳转到首页
const navigateToHome = () => {
  uni.switchTab({ url: '/pages/index/index' })
}

onMounted(async () => {
  // 启动倒计时
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      navigateToHome()
    }
  }, 1000)

  // 预加载用户信息
  await userStore.login().catch(() => {})
})

onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style lang="scss" scoped>
.splash-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.splash-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.skip-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 999;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: white;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 30rpx;
}
</style>
