import { useUserStore } from '@/store'
const userStore = useUserStore()

export default function useShare() {
	const share = reactive({
		title: '邀请您加入三择一生',
		path: '/pages/index/index',
		img: '',
	})
	share.title = `${userStore.userInfo.nickname}邀请您加入三择一生`
	share.path = `/pages/index/index?spread_code=${userStore.shareInfo.spreadCode}&spread_spid=${userStore.shareInfo.spreadSpid}`

	const setSharePath = (path : string) => {
		share.path = path

	}
	onShareAppMessage(() => {
		return share
	})
	onShareTimeline(() => {
		return share
	})

	return { onShareAppMessage, onShareTimeline, setSharePath }
}