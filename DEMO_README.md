# 赛事详情页面Demo

## 概述
已成功改造 `src/pages/shequn/detail.vue` 页面，使其符合您提供的微信小程序赛事详情页面设计。

## 主要改动

### 1. 页面结构
- **顶部横幅**: 大图展示赛事主题，包含赛事标题和日期
- **赛事信息卡片**: 显示赛事副标题、倒计时、分享量和地点信息
- **赛事简介**: 详细的赛事介绍和规则说明
- **底部按钮**: "加入赛事交流群" 和 "我要报名子" 两个操作按钮

### 2. Demo数据
创建了完整的demo数据，包括：
- 赛事标题：大邑安仁半程马拉松
- 赛事日期：2025.11.1
- 倒计时：3天23小时40分15秒
- 地点：四川省成都市大邑安仁古镇
- 详细的比赛信息（时间、地点、项目、报名方式等）
- 所有图片使用 `https://dummyimage.com/264x264/3c9cff/fff` 链接

### 3. 功能特性
- **分享功能**: 右上角分享和录制按钮
- **二维码弹窗**: 点击"加入赛事交流群"显示二维码
- **响应式设计**: 适配移动端显示
- **现代化UI**: 卡片式设计，圆角阴影效果

## 文件位置

### 主要文件
- `src/pages/shequn/detail.vue` - 原始页面（已改造）
- `src/pages/demo/race-detail.vue` - 独立demo页面

### 访问方式
1. **通过首页菜单**: 在首页底部菜单中添加了"赛事详情Demo"选项
2. **直接访问**: 导航到 `/pages/demo/race-detail`

## 样式特点

### 颜色方案
- 主色调：蓝色 (#3c9cff)
- 强调色：橙色 (#FF6B35) 和绿色 (#4CAF50)
- 背景色：浅灰 (#f5f5f5)
- 文字色：深灰 (#333333) 和中灰 (#666666)

### 设计元素
- 卡片式布局，20rpx圆角
- 柔和阴影效果
- 清晰的信息层级
- 易于阅读的字体大小和行高

## 技术实现

### 组件使用
- `wd-popup`: 二维码弹窗
- `wd-icon`: 图标组件
- 原生 `image` 和 `view` 组件

### 响应式特性
- 固定底部按钮
- 自适应图片显示
- 灵活的文字布局

## 下一步建议

1. **集成真实数据**: 将demo数据替换为API接口数据
2. **添加交互**: 实现真实的分享和报名功能
3. **优化性能**: 图片懒加载和缓存优化
4. **测试适配**: 在不同设备上测试显示效果

## 预览
要查看demo效果，请：
1. 启动项目
2. 在首页找到"赛事详情Demo"菜单项
3. 点击进入查看完整效果
