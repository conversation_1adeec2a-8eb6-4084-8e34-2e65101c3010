<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '赛事详情',
  },
}
</route>

<template>
  <view class="wrapper">
    <!-- 赛事横幅图 -->
    <view class="banner-section">
      <image :src="raceInfo.bannerImage" class="banner-image" mode="aspectFill" />
      <view class="banner-overlay">
        <view class="race-title">{{ raceInfo.title }}</view>
        <view class="race-date">{{ raceInfo.date }}</view>
      </view>
      <view class="share-btn" @click="onShare">
        <view class="share-icon">⋯</view>
        <view class="record-icon">📹</view>
      </view>
    </view>

    <!-- 赛事基本信息 -->
    <view class="race-info-card">
      <view class="race-subtitle">{{ raceInfo.subtitle }}</view>
      <view class="countdown-section">
        <view class="countdown-label">报名倒计时：</view>
        <view class="countdown-time">{{ raceInfo.countdown }}</view>
        <view class="share-count">分享量 {{ raceInfo.shareCount }}</view>
      </view>
      <view class="location">{{ raceInfo.location }}</view>
    </view>

    <!-- 赛事简介 -->
    <view class="section-card">
      <view class="section-title">赛事简介</view>
      <view class="banner-section">
        <image :src="raceInfo.bannerImage" class="intro-banner" mode="aspectFill" />
      </view>
      <view class="intro-content">
        <view class="intro-text">{{ raceInfo.introduction }}</view>

        <!-- 详细信息列表 -->
        <view class="detail-list">
          <view class="detail-item">
            <view class="detail-label">一、比赛时间</view>
            <view class="detail-value">{{ raceInfo.raceTime }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">二、比赛地点</view>
            <view class="detail-value">{{ raceInfo.raceLocation }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">三、竞赛项目及规模</view>
            <view class="detail-value">{{ raceInfo.raceProject }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">四、报名方式</view>
            <view
              class="detail-sub-item"
              v-for="(item, index) in raceInfo.registrationMethods"
              :key="index"
            >
              <view class="detail-value">{{ item }}</view>
            </view>
          </view>

          <view class="detail-item">
            <view class="detail-label">五、年龄要求</view>
            <view class="detail-value">{{ raceInfo.ageRequirement }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">六、领取赛事包的重要提醒：</view>
            <view
              class="detail-sub-item"
              v-for="(item, index) in raceInfo.packageReminders"
              :key="index"
            >
              <view class="detail-value">{{ item }}</view>
            </view>
          </view>

          <view class="detail-item">
            <view class="detail-label">七、参赛承诺</view>
            <view class="detail-value">{{ raceInfo.commitment }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-group" @click="onJoinGroup">加入赛事交流群</button>
      <button class="btn btn-register" @click="onRegister">我要报名子</button>
    </view>

    <!-- 二维码弹窗 -->
    <wd-popup
      v-model="showQRPopup"
      custom-style="border-radius: 30rpx;"
      :close-on-click-modal="false"
    >
      <view class="qr-popup">
        <view class="close-icon" @click="showQRPopup = false">
          <wd-icon name="close" size="20px" color="#999"></wd-icon>
        </view>
        <view class="popup-title">扫码加入赛事交流群</view>
        <view class="popup-content">
          <image class="qr-code" :src="raceInfo.qrCode" mode="aspectFit" />
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { useToast, useMessage } from 'wot-design-uni'

const message = useMessage()
const toast = useToast()

// 赛事信息数据
const raceInfo = ref({
  title: '大邑安仁半程马拉松',
  date: '2025.11.1',
  subtitle: '2025大邑安仁半程马拉松',
  countdown: '3天23小时40分15秒',
  shareCount: '2',
  location: '比赛地点：四川省成都市大邑安仁古镇',
  bannerImage: 'https://dummyimage.com/750x500/3c9cff/fff.png&text=大邑安仁半程马拉松2025.11.1',
  qrCode: 'https://dummyimage.com/400x400/3c9cff/fff.png&text=QR+Code',
  introduction:
    '2025成都大邑半程马拉松赛，将于2025年11月16日上午在四川省成都市大邑县举办，报名时间：2025年7月10日至10月16日，先到先得，额满即止。请关注官方网站：周末享受 或者地方汇报。',
  raceTime: '2025年11月16日上午9:00',
  raceLocation: '四川省成都市大邑安仁古镇',
  raceProject: '半程马拉松（21.0975公里）：7000人。',
  registrationMethods: [
    '1.报名时间：2025年7月10日至10月16日',
    '2.报名网站：周末享受',
    '3.年龄要求',
  ],
  ageRequirement:
    '半程马拉松参赛者需在16周岁以上70周岁以下（出生日期在2009年1月1日至1954年12月31日之间），其中18岁（出生日期在2007年1月1日之后）以下参赛者需要监护人或法定代理人签署参赛声明。',
  packageReminders: [
    '1.本人一年内（2024年11月16日之后）体检报告或县级以上医院体检报告，具体要求参考5.3部分）',
    '2.参赛承诺书。',
  ],
  commitment:
    '本人一年内（2024年11月16日之后）体检报告或县级以上医院体检报告，具体要求参考5.3部分）',
})

// 弹窗控制
const showQRPopup = ref(false)

// 页面方法
const onShare = () => {
  toast.show('分享功能')
}

const onJoinGroup = () => {
  showQRPopup.value = true
}

const onRegister = () => {
  toast.show('跳转到报名页面')
}

// 页面生命周期
onLoad(() => {
  console.log('页面加载')
})

onShow(() => {
  console.log('页面显示')
})
</script>

<style>
page {
  background-color: #f5f5f5;
}
</style>

<style lang="scss" scoped>
.wrapper {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

// 横幅图片区域
.banner-section {
  position: relative;
  width: 100%;
  height: 500rpx;

  .banner-image {
    width: 100%;
    height: 100%;
  }

  .banner-overlay {
    position: absolute;
    left: 40rpx;
    bottom: 60rpx;

    .race-title {
      font-size: 48rpx;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      margin-bottom: 10rpx;
    }

    .race-date {
      font-size: 36rpx;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    }
  }

  .share-btn {
    position: absolute;
    top: 40rpx;
    right: 40rpx;
    display: flex;
    gap: 20rpx;

    .share-icon,
    .record-icon {
      width: 60rpx;
      height: 60rpx;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 24rpx;
    }
  }
}

// 赛事信息卡片
.race-info-card {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .race-subtitle {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
  }

  .countdown-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .countdown-label {
      font-size: 28rpx;
      color: #666666;
    }

    .countdown-time {
      font-size: 28rpx;
      color: #ff6b35;
      font-weight: bold;
      margin-left: 10rpx;
      margin-right: auto;
    }

    .share-count {
      font-size: 24rpx;
      color: #999999;
      background: #f0f0f0;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
    }
  }

  .location {
    font-size: 26rpx;
    color: #666666;
  }
}

// 赛事简介卡片
.section-card {
  background: #ffffff;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 30rpx;
  }

  .intro-banner {
    width: 100%;
    height: 300rpx;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
  }

  .intro-content {
    .intro-text {
      font-size: 28rpx;
      line-height: 1.6;
      color: #666666;
      margin-bottom: 40rpx;
    }

    .detail-list {
      .detail-item {
        margin-bottom: 30rpx;

        .detail-label {
          font-size: 28rpx;
          font-weight: bold;
          color: #333333;
          margin-bottom: 10rpx;
        }

        .detail-value {
          font-size: 26rpx;
          line-height: 1.5;
          color: #666666;
          margin-bottom: 8rpx;
        }

        .detail-sub-item {
          margin-left: 20rpx;
          margin-bottom: 8rpx;

          .detail-value {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// 底部按钮
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background: #ffffff;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
  gap: 20rpx;

  .btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    border: none;

    &.btn-group {
      background: #4caf50;
      color: #ffffff;
    }

    &.btn-register {
      background: #ff6b35;
      color: #ffffff;
    }
  }
}

// 二维码弹窗
.qr-popup {
  position: relative;
  width: 600rpx;
  padding: 60rpx 40rpx 40rpx;
  background: #fff;
  border-radius: 30rpx;

  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    padding: 10rpx;
    cursor: pointer;
  }

  .popup-title {
    margin-bottom: 50rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  .popup-content {
    display: flex;
    justify-content: center;

    .qr-code {
      width: 400rpx;
      height: 400rpx;
      border-radius: 16rpx;
    }
  }
}
</style>
