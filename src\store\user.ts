import { defineStore } from 'pinia'
import { ref } from 'vue'
import { authType, authLogin, authBindingPhone, userinfo } from '@/service/public/index'
import { jwtDecode } from 'jwt-decode'

const initState = { nickname: '', avatar: '' }
const initLoginState = { bindName: false, expires_time: 0, token: '' }
const initShareInfo = { spreadCode: 0, spreadSpid: 0 }

export const useUserStore = defineStore(
  'user',
  () => {
    const loginInfo = ref<ILogin>({ ...initLoginState })
    const userInfo = ref<IUserInfo>({ ...initState })
    const shareInfo = ref<IShareInfo>({ ...initShareInfo })
    // 是否同意进入小程序
    const isAgreePrivacy = ref(false)

    // 修改

    // 设置用户登录信息
    const setLoginInfo = (val: ILogin) => {
      loginInfo.value = val
    }
    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }

    // 判断token是否有效
    const checkTokenValidity = (token: string): boolean => {
      try {
        const decoded: any = jwtDecode(token)
        const currentTime = Math.floor(Date.now() / 1000)
        return decoded.exp > currentTime
      } catch (error) {
        console.error('Invalid token:', error)
        return false
      }
    }

    const isLogined = computed(() => !!loginInfo.value.token)
    // 资料是否填写完整
    const isComplete = computed(() => !!userInfo.value.hobby)

    // 获取用户信息
    const getUserInfo = async () => {
      const [res, err] = await userinfo()
      if (res) {
        userInfo.value = { ...res.data }
      }
    }

    // 不获取手机号的登录
    const handleAuthLogin = async (key) => {
      const [res, err] = await authLogin(key)
      if (res) {
        loginInfo.value = { ...res.data }
        getUserInfo()
      }
    }
    // 强制获取手机号的登录
    const handleAuthBindingPhone = async (params) => {
      const data = {
        ...params,
        spread_code: shareInfo.value.spreadCode,
        spread_spid: shareInfo.value.spreadSpid,
      }
      const [res, err] = await authBindingPhone(params)
      if (res) {
        loginInfo.value = { ...res.data }
        getUserInfo()
      }
    }

    // 静默登录
    const login = async () => {
      // if (checkTokenValidity(loginInfo.value.token)) return
      const { code } = await uni.login()
      const [res, err] = await authType(code)
      if (res.data) {
        const { bindPhone, key } = res.data
        if (bindPhone) {
          await handleAuthBindingPhone(key)
        } else {
          await handleAuthLogin(key)
        }
      }
    }
    const loginAuthBindingPhone = async (params) => {
      uni.login({
        provider: 'weixin', // 使用微信登录
        success: async (loginRes) => {
          console.log(loginRes)
          const [res, err] = await authType(loginRes.code)
          if (res.data) {
            const { bindPhone, key } = res.data
            params.key = key
            handleAuthBindingPhone(params)
          }
        },
      })
    }
    /**
     * 返回客服信息
     */
    const getCustomerInfo = computed(() => {
      return {}
    })

    return {
      userInfo,
      shareInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      isComplete,
      login,
      loginInfo,
      setLoginInfo,
      getUserInfo,
      loginAuthBindingPhone,
      getCustomerInfo,
      isAgreePrivacy,
    }
  },
  {
    persist: true,
  },
)
