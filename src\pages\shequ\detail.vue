<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '社区详情',
    //开启下拉刷新
    enablePullDownRefresh: true,
    //页面上拉触底事件触发时距页面底部距离，单位只支持px
    onReachBottomDistance: 100,
  },
}
</route>

<template>
  <view class="wrapper pb-[30rpx]">
    <KeFu />

    <view class="card-swiper">
      <wd-swiper
        v-if="activityInfo.image_input.length > 0"
        :list="activityInfo.image_input"
        autoplay
        :height="375"
        v-model:current="current"
        @click="handleClick"
        @change="onChange"
      >
        <template #indicator="{ current, total }">
          <view class="custom-indicator">{{ current + 1 }}/{{ total }}</view>
        </template>
      </wd-swiper>
    </view>
    <!--活动详情 -->
    <view class="px-5">
      <view class="info">
        <!-- <view class="tags px-4">
          <view class="tag" v-for="(item, index) in activityInfo.biaoqian" :key="index">
            {{ item }}
          </view>
        </view> -->
        <view class="head px-4">
          <view class="title">{{ activityInfo.title }}</view>
          <!-- <view class="subtitle">
            <text class="text-[#F00707] text-[36rpx] font-bold">¥{{ activityInfo.price }}</text>
          </view> -->
          <view class="share">
            <button
              type="button"
              class="p-0 m-0 bg-transparent"
              open-type="share"
              hover-class="none"
            >
              <wd-img src="/static/images/icon-share.png" width="32" height="32" mode="widthFix" />
            </button>
          </view>
        </view>
        <view class="details px-4">
          <view class="row">
            <view class="label">
              <wd-img src="/static/images/icon-time.png" width="16" height="16" mode="widthFix" />
            </view>
            <view class="text">{{ activityInfo.add_time_date }}</view>
          </view>
          <!-- <view class="row">
            <view class="label">
              <wd-img
                src="/static/images/icon-address.png"
                width="16"
                height="16"
                mode="widthFix"
              />
            </view>
            <view class="text">{{ activityInfo.address }}</view>
          </view> -->
          <!-- <view class="row">
            <view class="label">
              <wd-img src="/static/images/icon-people.png" width="16" height="16" mode="widthFix" />
            </view>
            <view class="text">限{{ activityInfo.num }}人</view>
          </view>
          <view class="price">¥ {{ activityInfo.price }}</view> -->
        </view>
        <view class="line">
          <image src="/static/images/icon-line.png" mode="widthFix" class="w-full h-[28rpx] my-5" />
        </view>
        <view class="content">
          <mp-html :content="activityInfo.content" />
        </view>
        <!-- 评论 -->
        <view class="reply-section" v-if="goodsReplyList.length > 0">
          <view class="section-header">
            <text class="section-title">评论 ({{ totalReply }})</text>
            <text class="more"></text>
          </view>
          <view class="reply-list">
            <view class="reply-item" v-for="(item, index) in goodsReplyList" :key="index">
              <view class="user-info">
                <image :src="item.avatar" class="avatar" mode="aspectFill" />
                <view class="user-detail">
                  <text class="nickname">{{ item.nickname }}</text>
                  <text class="time">{{ item.add_time }}</text>
                </view>
              </view>
              <!-- <view class="score-list">
                <view class="score-item">
                  <text class="score-label">技能评分：</text>
                  <uni-rate :value="Number(item.skill_score)" size="14" readonly />
                </view>
                <view class="score-item">
                  <text class="score-label">服务评分：</text>
                  <uni-rate :value="Number(item.service_score)" size="14" readonly />
                </view>
                <view class="score-item">
                  <text class="score-label">质量评分：</text>
                  <uni-rate :value="Number(item.quality_score)" size="14" readonly />
                </view>
              </view> -->
              <view class="comment">{{ item.comment }}</view>
              <view class="pics" v-if="item.pics && item.pics.length">
                <image
                  v-for="(pic, picIndex) in item.pics.split(',')"
                  :key="picIndex"
                  :src="pic"
                  class="pic-item"
                  mode="aspectFill"
                />
              </view>
              <!-- <view class="reply" v-if="item.is_reply">
            <text class="reply-label">商家回复：</text>
            <text class="reply-content">{{ item.reply_content }}</text>
          </view> -->
            </view>
          </view>
          <view
            class="load-more"
            v-if="!pageData.loadend && goodsReplyList.length > 0"
            @click="loadCommentListArticle"
          >
            <text v-if="!pageData.loading">点击加载更多</text>
            <text v-else>加载中...</text>
          </view>
        </view>
      </view>
    </view>
    <view class="footer px-[30rpx] p-t-2 pd-ios">
      <!-- <view class="num">111</view> -->
      <view class="btns">
        <button type="button" class="btn btn-join" hover-class="none" v-if="activityInfo.visit > 0">
          浏览量 {{ activityInfo.visit }} 次
        </button>
        <button
          type="button"
          class="btn btn-submit"
          hover-class="button-hover"
          @click="onClickJoin"
        >
          评论
        </button>
      </view>
    </view>

    <!-- 添加邀请人信息弹窗 -->
    <wd-popup
      v-model="showInviterPopup"
      custom-style="border-radius: 30rpx;"
      :close-on-click-modal="false"
    >
      <view class="inviter-popup">
        <view class="close-icon" @click="showInviterPopup = false">
          <wd-icon name="close" size="20px" color="#999"></wd-icon>
        </view>
        <view class="popup-title">请填写您的神评</view>
        <view class="popup-content">
          <wd-textarea
            :height="200"
            v-model="inviterForm.comment"
            label=""
            placeholder="请填写您的神评"
            class="input-item"
          />
        </view>
        <view class="popup-footer">
          <button class="submit-btn" hover-class="button-hover" @click="handleInviterSubmit">
            确认
          </button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import {
  activityDetail,
  joinActivity,
  pay,
  commentListArticle,
  sendCommentArticle,
} from '@/service/user/index'
import { useToast, useMessage } from 'wot-design-uni'

import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
import KeFu from '@/components/KeFu/index.vue'

//
const userStore = useUserStore()
const { isComplete } = storeToRefs(userStore)

const { onShareAppMessage, onShareTimeline, setSharePath } = useShare()

const message = useMessage()
const toast = useToast()
const current = ref<number>(0)
const activityInfo = ref({
  id: '',
  image_input: [],
  title: '',
  content: '',
  start_time: '',
  address: '',
  price: 0,
  biaoqian: [],
  num: 0,
  add_time_date: '',
  visit: 0, // 浏览量
})
const activityId = ref(0)

const popupVisible = ref(false) // 弹窗是否可见
const goodsReplyList = ref([]) // 评价列表
const totalReply = ref(0) // 评价总数
const pageData = ref({
  page: 1, // 评价列表页码
  limit: 10, // 每页条数
  loading: false, // 是否正在加载更多
  loadend: false, // 是否加载完成
  state: 'loading' as 'loading' | 'finished', // 加载状态
})
const loadCommentListArticle = async () => {
  if (pageData.value.loadend) return
  pageData.value.loading = true

  const [res, err] = await commentListArticle(activityId.value, {
    page: pageData.value.page,
    limit: pageData.value.limit,
  })

  if (res) {
    console.log('res :>> ', res)
    totalReply.value = res.data.count
    const list = res.data.list
    const loadend = list.length < pageData.value.limit
    pageData.value.loadend = loadend
    pageData.value.page++
    pageData.value.state = loadend ? 'finished' : 'loading'
    goodsReplyList.value = [...goodsReplyList.value, ...list]
  }
  pageData.value.loading = false
}

// 添加邀请人表单相关的响应式数据
const showInviterPopup = ref(false)
const inviterForm = reactive({
  comment: '',
})

function handleClick(e) {
  console.log(e)
}
function onChange(e) {
  // console.log(e)
}
const restPage = () => {
  pageData.value.page = 1
  pageData.value.limit = 10
  pageData.value.loading = false
  pageData.value.loadend = false
  goodsReplyList.value = []
}
const loadDetail = async (id) => {
  restPage()
  const [res, err] = await activityDetail(id)
  if (res) {
    activityInfo.value = res.data
    loadCommentListArticle()
    setSharePath(`pages/shequn/detail?id=${activityInfo.value.id}`)
  }
}
onLoad(({ id }) => {
  activityId.value = id
  loadDetail(id)
})

// 监听下拉刷新
onPullDownRefresh(() => {
  loadDetail(activityId.value)
  setTimeout(() => {
    // 关闭下拉刷新
    uni.stopPullDownRefresh()
  }, 1000)
})
//
onReachBottom(() => {
  loadCommentListArticle()
})

// 报名
const onClickJoin = async () => {
  if (!isComplete.value) {
    return message
      .confirm({
        msg: '您的资料不完整，暂时无法享受全部功能',
        title: '系统提醒',
        confirmButtonText: '立即完善',
        cancelButtonText: '稍后再说',
      })
      .then(() => {
        uni.navigateTo({ url: '/pages/login/information' })
      })
      .catch(() => {
        console.log('点击了取消按钮')
      })
  }

  // 显示邀请人信息填写弹窗
  showInviterPopup.value = true
}

// 处理邀请人信息提交
const handleInviterSubmit = async () => {
  if (!inviterForm.comment) {
    return toast.warning('请填写您的神评')
  }
  // 关闭弹窗
  showInviterPopup.value = false

  // 调用报名接口,添加邀请人信息
  const [res, err] = await sendCommentArticle(activityInfo.value.id, {
    comment: inviterForm.comment,
  })

  if (res) {
    toast.show(res.msg)
  }
}
</script>

<style>
page {
  background-color: #f2f2f2;
}
</style>
<style lang="scss" scoped>
//
.custom-indicator {
  position: absolute;
  right: 333rpx;
  bottom: 166rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 49rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 25rpx;
  opacity: 0.59;
}

.card-swiper {
  --wot-swiper-radius: 0;
}

.info {
  box-sizing: border-box;
  width: 100%;
  padding: 38rpx 0;
  background: #ffffff;
  border-radius: 39rpx 39rpx 0rpx 0rpx;
  transform: translateY(-138rpx);

  .tags {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 22rpx;
    margin-bottom: 50rpx;

    .tag {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-size: 26rpx;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border-radius: 11rpx;
      border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }

  .head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 52rpx;

    .title {
      flex: 1;
      font-size: 34rpx;
      font-weight: 800;
      color: #303030;
    }

    .subtitle {
      margin-left: 16rpx;
      font-size: 36rpx;
      font-weight: 500;
      color: #7a7a7a;
    }
  }

  .details {
    position: relative;

    .price {
      position: absolute;
      right: 40rpx;
      bottom: 0;
      font-size: 39rpx;
      font-weight: 400;
      color: #303030;
      background: linear-gradient(-31deg, #ff94b2 0%, #f9225e 100%);
      -webkit-background-clip: text;
      /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent;
      /*给文字设置成透明*/
    }

    .row {
      display: flex;
      // align-items: center;
      margin-bottom: 26rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #303030;

      .label {
        flex-shrink: 0;
        margin-right: 16rpx;
      }

      .text {
        flex: 1;
      }
    }
  }

  .content {
    margin-top: 40rpx;
  }

  &__title {
    font-size: 30rpx;
    font-weight: 400;
    color: #333333;
  }

  &__row {
    display: flex;
    align-items: center;
    margin-top: 20rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #666666;

    &__label {
      width: 100rpx;
      color: #999999;
    }
  }
}

.activity-rules {
  margin-top: 20rpx;

  &__title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 26rpx;
    font-weight: 400;
    color: #999999;
  }

  .p {
    font-size: 26rpx;
    font-weight: 400;
    line-height: 40rpx;
    color: #999999;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: #fff;
  box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

  .btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60rpx;
      padding: 0;
      margin: 0;
      font-size: 30rpx;
      font-weight: 400;
      color: #ffffff;
      border-radius: 34rpx;
    }

    .btn-join {
      width: 300rpx;
      margin-right: 20rpx;
      color: #f9225e;
      background: linear-gradient(24deg, #f5efef, #f5efef, #f4efef);
      border-radius: 30rpx;
      border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }

    .btn-submit {
      width: 300rpx;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border-radius: 30rpx;
      border-image: linear-gradient(0deg, #ff94b2, #f9225e) 1 1;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }
}

.inviter-popup {
  position: relative;
  width: 600rpx;
  padding: 60rpx 40rpx 40rpx;
  background: #fff;
  border-radius: 30rpx;

  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    padding: 10rpx;
    cursor: pointer;
  }

  .popup-title {
    margin-bottom: 50rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }

  .popup-content {
    margin-bottom: 50rpx;

    .input-item {
      margin-bottom: 30rpx;

      :deep(.wd-input) {
        padding: 16rpx 24rpx;
        background: #f8f8f8;
        border-radius: 16rpx;
        transition: all 0.3s ease;
      }

      :deep(.wd-input__label) {
        font-weight: 500;
        color: #333;
      }

      :deep(.wd-input__inner) {
        height: 88rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        background: transparent;
        border: none;
        border-radius: 8rpx;
        transition: all 0.3s ease;
      }
    }
  }

  .popup-footer {
    margin-top: 50rpx;

    .submit-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      font-size: 32rpx;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(24deg, #ff94b2, #f9225e);
      border: none;
      border-radius: 44rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);
    }
  }
}

.reply-section {
  padding: 20rpx;
  margin-top: 10rpx;
  background-color: #fff;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.more {
  color: #e60012;
  cursor: pointer;
}

.reply-list {
  margin-bottom: 20rpx;
}

.reply-item {
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;
  border-bottom: #eeeeee solid 2rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 50%;
}

.user-detail {
  flex: 1;
}

.nickname {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.time {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.score-list {
  margin-bottom: 20rpx;
}

.score-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.score-label {
  width: 120rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.comment {
  margin-top: 10rpx;
  font-size: 28rpx;
  color: #333;
}

.pics {
  display: flex;
  margin-top: 10rpx;
}

.pic-item {
  width: 200rpx;
  height: 200rpx;
  margin-right: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;
}

.reply {
  margin-top: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.reply-label {
  font-weight: bold;
}

.reply-content {
  margin-left: 20rpx;
}

.load-more {
  padding: 20rpx;
  text-align: center;
  cursor: pointer;
  background-color: #fff;
}
</style>
